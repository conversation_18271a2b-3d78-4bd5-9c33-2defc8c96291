-- 产品分类和文档分类表结构
-- 支持三级产品分类：产品类型 -> 产品型号 -> 产品系列
-- 以及对应的资料目录分类管理

-- 产品分类表
CREATE TABLE IF NOT EXISTS ai_product_category (
    category_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '分类ID',
    category_name VARCHAR(100) NOT NULL COMMENT '分类名称',
    category_code VARCHAR(50) COMMENT '分类编码',
    category_type TINYINT NOT NULL DEFAULT 1 COMMENT '分类类型：1-产品类型，2-产品型号，3-产品系列',
    parent_id BIGINT DEFAULT 0 COMMENT '父级分类ID，0表示顶级分类',
    level_path VARCHAR(500) COMMENT '层级路径，用/分隔，如：/1/2/3/',
    sort_order INT DEFAULT 0 COMMENT '排序',
    description TEXT COMMENT '分类描述',
    icon VARCHAR(200) COMMENT '分类图标',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_by VARCHAR(64) COMMENT '创建人',
    update_by VARCHAR(64) COMMENT '修改人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
    PRIMARY KEY (category_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_category_type (category_type),
    INDEX idx_status (status),
    INDEX idx_del_flag (del_flag),
    INDEX idx_sort_order (sort_order),
    UNIQUE KEY uk_code_parent (category_code, parent_id, del_flag)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品分类表';

-- 文档分类表（资料目录）
CREATE TABLE IF NOT EXISTS ai_document_category (
    doc_category_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '文档分类ID',
    category_name VARCHAR(100) NOT NULL COMMENT '分类名称',
    category_code VARCHAR(50) COMMENT '分类编码',
    product_series_id BIGINT NOT NULL COMMENT '关联的产品系列ID',
    parent_id BIGINT DEFAULT 0 COMMENT '父级分类ID，0表示顶级分类',
    level_path VARCHAR(500) COMMENT '层级路径，用/分隔',
    sort_order INT DEFAULT 0 COMMENT '排序',
    description TEXT COMMENT '分类描述',
    icon VARCHAR(200) COMMENT '分类图标',
    is_system TINYINT DEFAULT 0 COMMENT '是否系统预定义：0-否，1-是',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_by VARCHAR(64) COMMENT '创建人',
    update_by VARCHAR(64) COMMENT '修改人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
    PRIMARY KEY (doc_category_id),
    INDEX idx_product_series_id (product_series_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_status (status),
    INDEX idx_del_flag (del_flag),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_system (is_system),
    UNIQUE KEY uk_code_series (category_code, product_series_id, del_flag)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档分类表（资料目录）';

-- 为ai_dataset_document表添加分类关联字段
ALTER TABLE ai_document
ADD COLUMN doc_category_id BIGINT COMMENT '文档分类ID' AFTER dataset_id,
ADD INDEX idx_doc_category_id (doc_category_id);

-- 添加外键约束（可选，根据实际需要决定是否启用）
-- ALTER TABLE ai_dataset_document 
-- ADD CONSTRAINT fk_doc_category 
-- FOREIGN KEY (doc_category_id) REFERENCES document_category(doc_category_id) ON DELETE SET NULL;

-- 插入示例数据

-- 产品类型（第一级）
INSERT IGNORE INTO ai_product_category (category_id, category_name, category_code, category_type, parent_id, level_path, sort_order, description, status, create_time, update_time) VALUES
(1, '商用产品', 'COMMERCIAL', 1, 0, '/1/', 1, '面向企业和商业用户的产品', 1, NOW(), NOW()),
(2, '家用产品', 'CONSUMER', 1, 0, '/2/', 2, '面向个人和家庭用户的产品', 1, NOW(), NOW());

-- 产品型号（第二级）- 商用产品下
INSERT IGNORE INTO ai_product_category (category_name, category_code, category_type, parent_id, level_path, sort_order, description, status, create_time, update_time) VALUES
('HP EliteBook', 'ELITEBOOK', 2, 1, '/1/3/', 1, 'HP商用笔记本电脑系列', 1, NOW(), NOW()),
('HP ProBook', 'PROBOOK', 2, 1, '/1/4/', 2, 'HP专业商用笔记本系列', 1, NOW(), NOW()),
('HP ZBook', 'ZBOOK', 2, 1, '/1/5/', 3, 'HP移动工作站系列', 1, NOW(), NOW());

-- 产品型号（第二级）- 家用产品下
INSERT IGNORE INTO ai_product_category (category_name, category_code, category_type, parent_id, level_path, sort_order, description, status, create_time, update_time) VALUES
('HP Pavilion', 'PAVILION', 2, 2, '/2/6/', 1, 'HP家用笔记本电脑系列', 1, NOW(), NOW()),
('HP ENVY', 'ENVY', 2, 2, '/2/7/', 2, 'HP高端家用笔记本系列', 1, NOW(), NOW());

-- 产品系列（第三级）- EliteBook下的具体型号
INSERT IGNORE INTO ai_product_category (category_name, category_code, category_type, parent_id, level_path, sort_order, description, status, create_time, update_time) VALUES
('HP EliteBook 6 G1i 13', 'ELITEBOOK_6_G1I_13', 3, 3, '/1/3/8/', 1, 'HP EliteBook 6 G1i 13英寸商用笔记本', 1, NOW(), NOW()),
('HP EliteBook 6 G1i 14', 'ELITEBOOK_6_G1I_14', 3, 3, '/1/3/9/', 2, 'HP EliteBook 6 G1i 14英寸商用笔记本', 1, NOW(), NOW());

-- 为产品系列自动创建资料目录分类
-- 为HP EliteBook 6 G1i 13创建资料目录
INSERT IGNORE INTO ai_document_category (category_name, category_code, product_series_id, parent_id, level_path, sort_order, description, is_system, status, create_time, update_time) VALUES
('产品资料', 'PRODUCT_DATA', 8, 0, '/1/', 1, '产品相关的技术资料和说明文档', 1, 1, NOW(), NOW()),
('产品图库', 'PRODUCT_GALLERY', 8, 0, '/2/', 2, '产品图片、渲染图等视觉资料', 1, 1, NOW(), NOW()),
('产品彩页', 'PRODUCT_BROCHURE', 8, 0, '/3/', 3, '产品宣传彩页和营销资料', 1, 1, NOW(), NOW()),
('产品证书', 'PRODUCT_CERTIFICATE', 8, 0, '/4/', 4, '产品认证证书和合规文档', 1, 1, NOW(), NOW()),
('Quick Specs', 'QUICK_SPECS', 8, 0, '/5/', 5, '产品快速规格说明书', 1, 1, NOW(), NOW()),
('图说产品', 'PRODUCT_ILLUSTRATION', 8, 0, '/6/', 6, '产品图解和功能说明', 1, 1, NOW(), NOW());

-- 为HP EliteBook 6 G1i 14创建资料目录
INSERT IGNORE INTO ai_document_category (category_name, category_code, product_series_id, parent_id, level_path, sort_order, description, is_system, status, create_time, update_time) VALUES
('产品资料', 'PRODUCT_DATA', 9, 0, '/7/', 1, '产品相关的技术资料和说明文档', 1, 1, NOW(), NOW()),
('产品图库', 'PRODUCT_GALLERY', 9, 0, '/8/', 2, '产品图片、渲染图等视觉资料', 1, 1, NOW(), NOW()),
('产品彩页', 'PRODUCT_BROCHURE', 9, 0, '/9/', 3, '产品宣传彩页和营销资料', 1, 1, NOW(), NOW()),
('产品证书', 'PRODUCT_CERTIFICATE', 9, 0, '/10/', 4, '产品认证证书和合规文档', 1, 1, NOW(), NOW()),
('Quick Specs', 'QUICK_SPECS', 9, 0, '/11/', 5, '产品快速规格说明书', 1, 1, NOW(), NOW()),
('图说产品', 'PRODUCT_ILLUSTRATION', 9, 0, '/12/', 6, '产品图解和功能说明', 1, 1, NOW(), NOW());
