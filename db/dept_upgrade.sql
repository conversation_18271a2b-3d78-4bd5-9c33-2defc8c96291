-- 部门表结构升级脚本
-- 添加部门群组邮箱和部门办公电话字段

-- 检查并添加部门群组邮箱字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'sys_dept' 
     AND COLUMN_NAME = 'group_email') = 0,
    'ALTER TABLE sys_dept ADD COLUMN group_email VARCHAR(100) COMMENT "部门群组邮箱"',
    'SELECT "group_email column already exists" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加部门办公电话字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'sys_dept' 
     AND COLUMN_NAME = 'office_phone') = 0,
    'ALTER TABLE sys_dept ADD COLUMN office_phone VARCHAR(20) COMMENT "部门办公电话"',
    'SELECT "office_phone column already exists" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新字段注释，明确区分用途
ALTER TABLE sys_dept MODIFY COLUMN phone VARCHAR(20) COMMENT '部门联系电话';
ALTER TABLE sys_dept MODIFY COLUMN email VARCHAR(100) COMMENT '部门联系邮箱';

-- 如果表不存在，创建完整的部门表
CREATE TABLE IF NOT EXISTS sys_dept (
    dept_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '部门ID',
    name VARCHAR(50) NOT NULL COMMENT '部门名称',
    phone VARCHAR(20) COMMENT '部门联系电话',
    email VARCHAR(100) COMMENT '部门联系邮箱',
    group_email VARCHAR(100) COMMENT '部门群组邮箱',
    office_phone VARCHAR(20) COMMENT '部门办公电话',
    sort_order INT DEFAULT 0 COMMENT '排序',
    create_by VARCHAR(64) COMMENT '创建人',
    update_by VARCHAR(64) COMMENT '修改人',
    create_time DATETIME COMMENT '创建时间',
    update_time DATETIME COMMENT '修改时间',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
    parent_id BIGINT DEFAULT 0 COMMENT '父级部门ID',
    PRIMARY KEY (dept_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_del_flag (del_flag)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门管理表';

-- 插入默认的根部门（如果不存在）
INSERT IGNORE INTO sys_dept (dept_id, name, parent_id, sort_order, del_flag, create_time, update_time) 
VALUES (1, '总公司', 0, 0, '0', NOW(), NOW());

-- 示例数据（可选）
INSERT IGNORE INTO sys_dept (name, parent_id, sort_order, phone, email, group_email, office_phone, del_flag, create_time, update_time) 
VALUES 
('技术部', 1, 1, '010-12345678', '<EMAIL>', '<EMAIL>', '010-12345679', '0', NOW(), NOW()),
('市场部', 1, 2, '010-12345680', '<EMAIL>', '<EMAIL>', '010-12345681', '0', NOW(), NOW()),
('人事部', 1, 3, '010-12345682', '<EMAIL>', '<EMAIL>', '010-12345683', '0', NOW(), NOW()),
('财务部', 1, 4, '010-12345684', '<EMAIL>', '<EMAIL>', '010-12345685', '0', NOW(), NOW());
