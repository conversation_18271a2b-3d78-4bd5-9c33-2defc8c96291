services:
  zhan-ai:
    build:
      context: zhan-ai
    container_name: zhan-ai
    environment:
      - TZ=Asia/Shanghai
      - MYSQL_HOST=zhan-mysql
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=zhan_ai_enterprise
      - MYSQL_USERNAME=root
      - MYSQL_PASSWORD=root
      - REDIS_HOST=zhan-redis
      - REDIS_PORT=6379
      - REDIS_DATABASE=0
      - REDIS_PASSWORD=123456
    image: zhan-ai
    ports:
      - 8070:8070
    restart: always
    volumes:
      - ./logs:/app/logs
    networks:
      - zhan_ai_default

  zhan-mysql:
    build:
      context: ./db
    environment:
      MYSQL_ROOT_HOST: "%"
      MYSQL_ROOT_PASSWORD: root
    restart: always
    container_name: zhan-mysql
    image: zhan-mysql
    command: --lower_case_table_names=1
    networks:
      - zhan_ai_default

  zhan-redis:
    container_name: zhan-redis
    image: redis:6.2.6
    restart: always
    command: redis-server --requirepass 123456
    networks:
      - zhan_ai_default

networks:
  zhan_ai_default:
    name:  zhan_ai_default
    driver: bridge