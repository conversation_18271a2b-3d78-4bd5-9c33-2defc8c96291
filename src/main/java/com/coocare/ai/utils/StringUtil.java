package com.coocare.ai.utils;

import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * <AUTHOR>
 * @create 2021/1/27 15:12
 */

public class StringUtil {

    /**
     * 转换逗号分隔的long字符串为Long数字
     *
     * @param strs
     * @return
     */
    public static List<Long> parseLong(String strs) {
        List<Long> ls = new ArrayList<>();
        if (!StringUtils.isEmpty(strs)) {
            for (String str : strs.split(",")) {
                try {
                    ls.add(Long.valueOf(str));
                } catch (NumberFormatException e) {
                }
            }
        }
        return ls;
    }
}
