package com.coocare.ai.utils;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Describe: 分 页 参 数 封 装
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageDomain {

    /**
     * 当前页
     */
    @Schema(description = "当前页", defaultValue = "1")
    private Integer pageNo = 1;

    /**
     * 每页数量
     */
    @Schema(description = "每页数量", defaultValue = "10")
    private Integer pageSize = 10;

    /**
     * 获取开始的数据行
     */
    public Integer start() {
        return (this.pageNo - 1) * this.pageSize;
    }

    /**
     * 获取结束的数据行
     */
    public Integer end() {
        return this.pageNo * this.pageSize;
    }

}
