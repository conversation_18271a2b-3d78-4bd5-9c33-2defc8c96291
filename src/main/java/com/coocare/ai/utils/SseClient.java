//package com.coocare.ai.utils;
//
//import cn.hutool.core.util.StrUtil;
//import com.coocare.ai.config.exception.RRException;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
//
//import java.io.IOException;
//import java.util.Map;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.concurrent.ScheduledFuture;
//
/// **
// * <AUTHOR>
// * @date 2025/1/16
// */
//
//@Slf4j
//@Component
//public class SseClient {
//
//    private static final Map<Long, SseEmitter> sseEmitterMap = new ConcurrentHashMap<>();
//
//    /**
//     * 创建连接
//     */
//    public SseEmitter createSse(Long uid) {
//        //默认30秒超时,设置为0L则永不超时
//        SseEmitter sseEmitter = new SseEmitter(0L);
//        //完成后回调
//        sseEmitter.onCompletion(() -> {
//            log.info("[{}]结束连接...................", uid);
//            sseEmitterMap.remove(uid);
//        });
//        //超时回调
//        sseEmitter.onTimeout(() -> {
//            log.info("[{}]连接超时...................", uid);
//        });
//        //异常回调
//        sseEmitter.onError(
//                throwable -> {
//                    try {
//                        log.info("[{}]连接异常,{}", uid, throwable.toString());
//                        sseEmitter.send(SseEmitter.event()
//                                .id(uid + "")
//                                .name("发生异常！")
//                                .data("发生异常请重试！")
//                                .reconnectTime(3000));
//                        sseEmitterMap.put(uid, sseEmitter);
//                    } catch (IOException e) {
//                        e.printStackTrace();
//                    }
//                }
//        );
//        try {
//            sseEmitter.send(SseEmitter.event().reconnectTime(5000));
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        sseEmitterMap.put(uid, sseEmitter);
//        log.info("[{}]创建sse连接成功！", uid);
//        return sseEmitter;
//    }
//
//    /**
//     * 给指定用户发送消息
//     */
//    public boolean sendMessage(Long uid, String messageId, String message) {
//        if (StrUtil.isBlank(message)) {
//            log.info("参数异常，msg为null");
//            return false;
//        }
//        SseEmitter sseEmitter = sseEmitterMap.get(uid);
//        if (sseEmitter == null) {
//            log.info("消息推送失败uid:[{}],没有创建连接，请重试。", uid);
//            return false;
//        }
//        try {
//            sseEmitter.send(SseEmitter.event().id(messageId).reconnectTime(60 * 1000L).data(message));
//            log.info("用户{},消息id:{},推送成功:{}", uid, messageId, message);
//            return true;
//        } catch (Exception e) {
//            sseEmitterMap.remove(uid);
//            log.info("用户{},消息id:{},推送异常:{}", uid, messageId, e.getMessage());
//            sseEmitter.complete();
//            return false;
//        }
//    }
//
//    public boolean sendMessage(Long uid, String message) {
//        if (StrUtil.isBlank(message)) {
//            log.info("参数异常，msg为null");
//            return false;
//        }
//        SseEmitter sseEmitter = sseEmitterMap.get(uid);
//        if (sseEmitter == null) {
//            log.info("消息推送失败uid:[{}],没有创建连接，请重试。", uid);
//            return false;
//        }
//        try {
//            sseEmitter.send(SseEmitter.event().reconnectTime(60 * 1000L).data(message));
//            log.info("用户{},消息id:{},推送成功:{}", uid, message);
//            return true;
//        } catch (Exception e) {
//            sseEmitterMap.remove(uid);
//            log.info("用户{},消息id:{},推送异常:{}", uid, e.getMessage());
//            sseEmitter.complete();
//            return false;
//        }
//    }
//
//
//    /**
//     * 断开
//     *
//     * @param uid
//     */
//    public void closeSse(Long uid) {
//        if (sseEmitterMap.containsKey(uid)) {
//            SseEmitter sseEmitter = sseEmitterMap.get(uid);
//            sseEmitter.complete();
//            sseEmitterMap.remove(uid);
//        } else {
//            log.info("用户{} 连接已关闭", uid);
//        }
//
//    }
//
//    /**
//     * 判断Session是否存在
//     *
//     * @param id 客户端ID
//     * @return
//     */
//    public static boolean exist(Long id) {
//        return sseEmitterMap.get(id) == null;
//    }
//
//    /**
//     * SseEmitter onCompletion 后执行的逻辑
//     *
//     * @param id     客户端ID
//     * @param future
//     */
//    public static void onCompletion(Long id, ScheduledFuture<?> future) {
//        sseEmitterMap.remove(id);
//        if (future != null) {
//            // SseEmitter断开后需要中断心跳发送
//            future.cancel(true);
//        }
//    }
//
//    /**
//     * SseEmitter onTimeout 或 onError 后执行的逻辑
//     *
//     * @param id
//     * @param e
//     */
//    public static void onError(Long id, RRException e) {
//        final SseEmitter emitter = sseEmitterMap.get(id);
//        if (emitter != null) {
//            emitter.completeWithError(e);
//        }
//    }
//}
