package com.coocare.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Schema
@Data
@TableName(value = "ai_agent")
public class AiAgent implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "agent_id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long agentId;

    /**
     * 智能体名称
     */
    @TableField(value = "agent_name")
    @Schema(description="智能体名称")
    private String agentName;

    /**
     * 介绍
     */
    @TableField(value = "agent_intro")
    @Schema(description="介绍")
    private String agentIntro;

    /**
     * 是否启用
     */
    @TableField(value = "`enable`")
    @Schema(description="是否启用")
    private Byte enable;
}