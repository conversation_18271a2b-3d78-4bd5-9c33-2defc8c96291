package com.coocare.ai.entity.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "用户角色分配DTO")
public class UserRoleAssignDTO {

    @Schema(description = "用户id")
    @NotNull
    private Long userId;

    @Schema(description = "角色ids")
    @NotEmpty
    private List<Long> roleIds;
}
