package com.coocare.ai.entity.sys;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_user")
@Schema(description = "用户表")
public class SysUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户ID")
    @TableId(value = "user_id", type = IdType.ASSIGN_ID)
    private Long userId;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "盐值")
    private String salt;

    @Schema(description = "电话号码")
    private String phone;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "邮箱地址")
    private String email;

    @Schema(description = "移动电话")
    private String mobile;

    @Schema(description = "工号")
    private String employeeId;

    @Schema(description = "所属部门ID")
    private Long deptId;

    @Schema(description = "锁定标记，0未锁定，1已锁定")
    private String lockFlag;

    @Schema(description = "是否启用（true:启用,false:停用）")
    private Boolean enable;

    @Schema(description = "创建人", hidden = true)
    private String createBy;

    @Schema(description = "创建时间", hidden = true)
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改人", hidden = true)
    private String updateBy;

    @Schema(description = "更新时间", hidden = true)
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "删除标志，0未删除，1已删除", hidden = true)
    private String delFlag;

}
