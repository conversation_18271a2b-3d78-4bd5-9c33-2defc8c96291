package com.coocare.ai.entity.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_dict")
@Schema(description = "字典表")
public class SysDict implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonIgnore
    @Schema(description = "编号")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "字典类型")
    private String dictType;

    @Schema(description = "描述")
    private String description;

    @JsonIgnore
    @Schema(description = "创建人")
    private String createBy;

    @JsonIgnore
    @Schema(description = "修改人")
    private String updateBy;

    @JsonIgnore
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @JsonIgnore
    @Schema(description = "更新时间")
    @TableField(value = "create_time", fill = FieldFill.UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @JsonIgnore
    @Schema(description = "备注信息")
    private String remarks;

    @Schema(description = "系统标志")
    @JsonIgnore
    private String systemFlag;

    @JsonIgnore
    @Schema(description = "删除标志")
    private String delFlag;


}
