package com.coocare.ai.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 系统配置表实体类
 * 用于存储系统级别的配置参数，支持动态配置管理
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Schema(description = "系统配置管理")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "sys_config")
public class SysConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置ID - 主键，系统自动生成
     */
    @TableId(value = "config_id", type = IdType.ASSIGN_ID)
    @Schema(description = "配置ID，系统自动生成的唯一标识")
    private Long configId;

    /**
     * 参数键名 - 配置参数的唯一标识符
     */
    @TableField(value = "param_key")
    @Schema(description = "参数键名，配置参数的唯一标识符")
    @NotBlank(message = "参数键名不能为空")
    @Size(max = 100, message = "参数键名长度不能超过100个字符")
    private String paramKey;

    /**
     * 参数值 - 配置参数的具体值
     */
    @TableField(value = "param_value")
    @Schema(description = "参数值，配置参数的具体值")
    @Size(max = 2000, message = "参数值长度不能超过2000个字符")
    private String paramValue;

    /**
     * 备注说明 - 配置参数的用途说明
     */
    @TableField(value = "remark")
    @Schema(description = "备注说明，配置参数的用途说明")
    @Size(max = 500, message = "备注说明长度不能超过500个字符")
    private String remark;
}