package com.coocare.ai.entity.sys;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 公共参数配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_public_param")
@Schema(description = "公共参数配置表")
public class SysPublicParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "编号")
    @TableId(value = "public_id", type = IdType.ASSIGN_ID)
    private Long publicId;

    @Schema(description = "名称")
    private String publicName;

    @Schema(description = "键")
    private String publicKey;

    @Schema(description = "值")
    private String publicValue;

    @Schema(description = "状态，0禁用，1启用")
    private String status;

    @Schema(description = "校验码")
    private String validateCode;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "修改人")
    private String updateBy;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "类型，0未知，1系统，2业务")
    private String publicType;

    @Schema(description = "系统标识，0非系统，1系统")
    private String systemFlag;

    @Schema(description = "删除标记，0未删除，1已删除")
    private String delFlag;

}
