package com.coocare.ai.entity.sys.dto;

import com.coocare.ai.entity.sys.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created with IntelliJ IDEA.
 *
 * <AUTHOR>
 * @create 2024/4/22 下午3:22
 */

@Data
@Schema(description = "系统用户传输对象")
@EqualsAndHashCode(callSuper = true)
public class UserDTO extends SysUser {

    /**
     * 角色ID
     */
    @Schema(description = "角色id集合")
    private String role;

    /**
     * 岗位ID
     */
    private String post;

    /**
     * 新密码
     */
    @Schema(description = "新密码")
    private String newpassword1;

}
