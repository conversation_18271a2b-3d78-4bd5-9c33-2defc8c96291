package com.coocare.ai.entity.sys;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 系统角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_role")
@Schema(description = "系统角色表")
public class SysRole implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "角色ID")
    @TableId(value = "role_id", type = IdType.ASSIGN_ID)
    private Long roleId;

    @Schema(description = "角色名称")
    @NotEmpty(message = "角色名称不能空")
    private String roleName;

    @Schema(description = "角色编码")
    private String roleCode;

    @Schema(description = "角色描述")
    private String roleDesc;

    @Schema(description = "数据权限类型，0全部，1自定义，2本部门及以下，3本部门，4仅本人", hidden = true)
    private String dsType;

    @Schema(description = "数据权限范围", hidden = true)
    private String dsScope;

    @Schema(description = "是否启用", hidden = true)
    private Boolean enable;

    @Schema(description = "创建人", hidden = true)
    private String createBy;

    @Schema(description = "创建时间", hidden = true)
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改人", hidden = true)
    private String updateBy;

    @Schema(description = "更新时间", hidden = true)
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "删除标志，0未删除，1已删除", hidden = true)
    private String delFlag;
}
