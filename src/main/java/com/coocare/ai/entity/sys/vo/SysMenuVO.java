package com.coocare.ai.entity.sys.vo;

import com.coocare.ai.entity.sys.SysMenu;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * SysMenuVO
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023-10-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysMenuVO extends SysMenu {

    @Schema(description = "获取子菜单")
    private List<SysMenuVO> children;

}
