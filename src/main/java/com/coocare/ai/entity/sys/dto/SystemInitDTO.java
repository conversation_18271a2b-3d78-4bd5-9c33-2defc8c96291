package com.coocare.ai.entity.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 系统初始化DTO
 * 用于系统首次启动时的初始化配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@Schema(description = "系统初始化配置")
public class SystemInitDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    // ==================== 管理员账号信息 ====================
    
    @Schema(description = "管理员用户名")
    @NotBlank(message = "管理员用户名不能为空")
    @Size(min = 3, max = 20, message = "管理员用户名长度必须在3-20个字符之间")
    private String adminUsername;

    @Schema(description = "管理员密码")
    @NotBlank(message = "管理员密码不能为空")
    @Size(min = 6, max = 20, message = "管理员密码长度必须在6-20个字符之间")
    private String adminPassword;

    @Schema(description = "管理员姓名")
    @NotBlank(message = "管理员姓名不能为空")
    @Size(max = 50, message = "管理员姓名长度不能超过50个字符")
    private String adminName;

    @Schema(description = "管理员邮箱")
    @Email(message = "管理员邮箱格式不正确")
    @Size(max = 100, message = "管理员邮箱长度不能超过100个字符")
    private String adminEmail;

    @Schema(description = "管理员手机号")
    @Size(max = 20, message = "管理员手机号长度不能超过20个字符")
    private String adminPhone;

    // ==================== 企业信息 ====================
    
    @Schema(description = "企业名称")
    @NotBlank(message = "企业名称不能为空")
    @Size(max = 100, message = "企业名称长度不能超过100个字符")
    private String companyName;

    @Schema(description = "企业税号/统一社会信用代码")
    @NotBlank(message = "企业税号不能为空")
    @Size(max = 50, message = "企业税号长度不能超过50个字符")
    private String taxNumber;

    @Schema(description = "省")
    @NotBlank(message = "省不能为空")
    private String province;

    @Schema(description = "市")
    @NotBlank(message = "市不能为空")
    private String city;

    @Schema(description = "区")
    @NotBlank(message = "区不能为空")
    private String district;

    @Schema(description = "企业地址")
    @Size(max = 200, message = "企业地址长度不能超过200个字符")
    private String companyAddress;

    @Schema(description = "企业联系电话")
    @Size(max = 20, message = "企业联系电话长度不能超过20个字符")
    private String companyPhone;

    @Schema(description = "企业邮箱")
    @Email(message = "企业邮箱格式不正确")
    @Size(max = 100, message = "企业邮箱长度不能超过100个字符")
    private String companyEmail;

    @Schema(description = "企业Logo URL")
    @Size(max = 500, message = "企业Logo URL长度不能超过500个字符")
    private String companyLogo;

    // ==================== 行业信息 ====================
    
    @Schema(description = "所属行业")
    @NotBlank(message = "所属行业不能为空")
    @Size(max = 50, message = "所属行业长度不能超过50个字符")
    private String industry;

    @Schema(description = "行业细分")
    @Size(max = 100, message = "行业细分长度不能超过100个字符")
    private String industrySubcategory;

    @Schema(description = "企业规模（员工数量）")
    private String companyScale;

    // ==================== 智能体配置 ====================
    
    @Schema(description = "启用的智能体列表")
    private List<Long> enabledAgents;

    @Schema(description = "默认智能体")
    @Size(max = 50, message = "默认智能体名称长度不能超过50个字符")
    private String defaultAgent;

    // ==================== 系统配置 ====================
    
    @Schema(description = "时区设置")
    private String timeZone = "Asia/Shanghai";

    @Schema(description = "语言设置")
    private String language = "zh-CN";

    @Schema(description = "是否启用邮件通知")
    private Boolean enableEmailNotification = true;

    @Schema(description = "是否启用短信通知")
    private Boolean enableSmsNotification = false;

}
