package com.coocare.ai.entity.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 字典项
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_dict_item")
@Schema(description = "字典项")
public class SysDictItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "编号")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "字典ID")
    private Long dictId;

    @Schema(description = "字典项值")
    private String itemValue;

    @Schema(description = "字典项名称")
    private String label;

    @Schema(description = "字典类型")
    private String dictType;

    @Schema(description = "字典项描述")
    private String description;

    @Schema(description = "数据范围")
    private String scope;

    @Schema(description = "数据系数")
    private String coefficient;

    @Schema(description = "属性")
    private String readable;

    @Schema(description = "数据步幅")
    private Integer step;

    @Schema(description = "备注信息")
    private String remarks;

    @Schema(description = "排序（升序）")
    private Integer sortOrder;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "修改人")
    private String updateBy;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @TableField(value = "create_time", fill = FieldFill.UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "删除标志")
    private String delFlag;

    @Schema(description = "是否上报历史")
    private Boolean history;

    @TableField(exist = false)
    private String value;

}
