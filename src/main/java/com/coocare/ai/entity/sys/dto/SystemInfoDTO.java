package com.coocare.ai.entity.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 系统信息DTO
 * 用于返回服务器的硬件和系统信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@Schema(description = "系统信息")
public class SystemInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "CPU信息")
    private CpuInfo cpu;

    @Schema(description = "内存信息")
    private MemoryInfo memory;

    @Schema(description = "磁盘信息")
    private List<DiskInfo> disks;

    @Schema(description = "网络信息")
    private List<NetworkInfo> networks;

    @Schema(description = "系统信息")
    private SystemBasicInfo system;

    /**
     * CPU信息
     */
    @Data
    @Schema(description = "CPU信息")
    public static class CpuInfo {
        @Schema(description = "CPU型号")
        private String model;

        @Schema(description = "CPU核心数")
        private int cores;

        @Schema(description = "CPU逻辑处理器数")
        private int logicalProcessors;

        @Schema(description = "CPU使用率（百分比）")
        private double usage;

        @Schema(description = "CPU频率（MHz）")
        private long frequency;

        @Schema(description = "CPU温度（摄氏度）")
        private double temperature;

        @Schema(description = "CPU架构")
        private String architecture;
    }

    /**
     * 内存信息
     */
    @Data
    @Schema(description = "内存信息")
    public static class MemoryInfo {
        @Schema(description = "总内存（字节）")
        private long total;

        @Schema(description = "已使用内存（字节）")
        private long used;

        @Schema(description = "可用内存（字节）")
        private long available;

        @Schema(description = "内存使用率（百分比）")
        private double usage;

        @Schema(description = "总内存（格式化）")
        private String totalFormatted;

        @Schema(description = "已使用内存（格式化）")
        private String usedFormatted;

        @Schema(description = "可用内存（格式化）")
        private String availableFormatted;
    }

    /**
     * 磁盘信息
     */
    @Data
    @Schema(description = "磁盘信息")
    public static class DiskInfo {
        @Schema(description = "磁盘名称")
        private String name;

        @Schema(description = "挂载点")
        private String mountPoint;

        @Schema(description = "文件系统类型")
        private String fileSystem;

        @Schema(description = "总容量（字节）")
        private long total;

        @Schema(description = "已使用容量（字节）")
        private long used;

        @Schema(description = "可用容量（字节）")
        private long available;

        @Schema(description = "使用率（百分比）")
        private double usage;

        @Schema(description = "总容量（格式化）")
        private String totalFormatted;

        @Schema(description = "已使用容量（格式化）")
        private String usedFormatted;

        @Schema(description = "可用容量（格式化）")
        private String availableFormatted;
    }

    /**
     * 网络信息
     */
    @Data
    @Schema(description = "网络信息")
    public static class NetworkInfo {
        @Schema(description = "网络接口名称")
        private String name;

        @Schema(description = "显示名称")
        private String displayName;

        @Schema(description = "MAC地址")
        private String macAddress;

        @Schema(description = "IP地址列表")
        private List<String> ipAddresses;

        @Schema(description = "是否启用")
        private boolean enabled;

        @Schema(description = "接收字节数")
        private long bytesReceived;

        @Schema(description = "发送字节数")
        private long bytesSent;

        @Schema(description = "接收包数")
        private long packetsReceived;

        @Schema(description = "发送包数")
        private long packetsSent;

        @Schema(description = "下载速度（字节/秒）")
        private long downloadSpeed;

        @Schema(description = "上传速度（字节/秒）")
        private long uploadSpeed;

        @Schema(description = "下载速度（格式化）")
        private String downloadSpeedFormatted;

        @Schema(description = "上传速度（格式化）")
        private String uploadSpeedFormatted;
    }

    /**
     * 系统基本信息
     */
    @Data
    @Schema(description = "系统基本信息")
    public static class SystemBasicInfo {
        @Schema(description = "操作系统名称")
        private String osName;

        @Schema(description = "操作系统版本")
        private String osVersion;

        @Schema(description = "操作系统架构")
        private String osArch;

        @Schema(description = "Java版本")
        private String javaVersion;

        @Schema(description = "JVM名称")
        private String jvmName;

        @Schema(description = "JVM版本")
        private String jvmVersion;

        @Schema(description = "系统启动时间")
        private String bootTime;

        @Schema(description = "系统运行时间")
        private String uptime;

        @Schema(description = "当前时区")
        private String timeZone;

        @Schema(description = "当前时间")
        private String currentTime;

        @Schema(description = "主机名")
        private String hostname;

        @Schema(description = "用户名")
        private String username;

        @Schema(description = "用户目录")
        private String userHome;

        @Schema(description = "临时目录")
        private String tempDir;
    }
}
