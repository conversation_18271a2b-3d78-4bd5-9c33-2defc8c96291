package com.coocare.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 知识库
 */
@Schema(description="知识库")
@Data
@TableName(value = "ai_dataset")
public class AiDataset implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 知识库ID
     */
    @TableId(value = "dataset_id", type = IdType.ASSIGN_ID)
    @Schema(description="知识库ID")
    private Long datasetId;

    /**
     * 知识库标题
     */
    @TableField(value = "title")
    @Schema(description="知识库标题")
    private String title;

    /**
     * 知识库图标
     */
    @TableField(value = "icon")
    @Schema(description="知识库图标")
    private String icon;

    /**
     * 索引方式
     */
    @TableField(value = "indexing_technique")
    @Schema(description="索引方式")
    private String indexingTechnique;

    /**
     * 处理规则
     */
    @TableField(value = "process_rule")
    @Schema(description="处理规则")
    private String processRule;

    /**
     * 索引内容的形式  
     */
    @TableField(value = "doc_form")
    @Schema(description="索引内容的形式  ")
    private String docForm;

    /**
     * 检索模式
     */
    @TableField(value = "retrieval_model")
    @Schema(description="检索模式")
    private Integer retrievalModel;

    /**
     * 对应的实际ID
     */
    @TableField(value = "backend_id")
    @Schema(description="对应的实际ID")
    private String backendId;
}