//package com.coocare.ai.controller;
//
//import com.coocare.ai.utils.SseClient;
//import lombok.RequiredArgsConstructor;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
//
//import java.io.IOException;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.concurrent.ConcurrentHashMap;
//
//
//@RestController
//@RequestMapping("/sse")
//@RequiredArgsConstructor
//public class SSEmitterController {
//
//    private final SseClient sseClient;
//
//
//    @GetMapping("/stream")
//    public SseEmitter stream(@RequestParam("uid")Long uid) {
//        // 3S超时
//        SseEmitter emitter = new SseEmitter(10000L);
//
//        // 注册回调函数，处理服务器向客户端推送的消息
//        emitter.onCompletion(() -> {
//            System.out.println("Connection completed");
//            // 在连接完成时执行一些清理工作
//            sseEmitters.remove(uid);
//        });
//
//        emitter.onTimeout(() -> {
//            System.out.println("Connection timeout");
//            // 在连接超时时执行一些处理
//            emitter.complete();
//            sseEmitters.remove(uid);
//        });
//
//        //// 在后台线程中模拟实时数据
//        //new Thread(() -> {
//        //    try {
//        //        for (int i = 0; i < 10; i++) {
//        //            emitter.send(SseEmitter.event().name("message").data("[" + new Date() + "] Data #" + i));
//        //            Thread.sleep(1000);
//        //        }
//        //        emitter.complete(); // 数据发送完成后，关闭连接
//        //    } catch (IOException | InterruptedException e) {
//        //        emitter.completeWithError(e); // 发生错误时，关闭连接并报错
//        //    }
//        //}).start();
//
//        sseEmitters.put(uid, emitter);
//
//        return emitter;
//    }
//}
