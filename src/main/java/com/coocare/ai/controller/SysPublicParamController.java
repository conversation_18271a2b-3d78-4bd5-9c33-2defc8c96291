package com.coocare.ai.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.entity.sys.SysPublicParam;
import com.coocare.ai.service.ISysPublicParamService;
import com.coocare.ai.utils.PageDomain;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 公共参数配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Tag(name = "公共参数管理")
@RequiredArgsConstructor
@RestController
@RequestMapping("/sys/param")
public class SysPublicParamController {

    private final ISysPublicParamService publicParamService;

    @Operation(summary = "查询公共参数值")
    @GetMapping("/{publicKey}")
    public AjaxResult<?> publicKey(@PathVariable("publicKey") String publicKey) {
        return AjaxResult.ok(publicParamService.getSysPublicParamKeyToValue(publicKey));
    }

    /**
     * 分页查询
     *
     * @param page           分页对象
     * @param sysPublicParam 公共参数
     * @return
     */
    @Operation(summary = "分页查询")
    @GetMapping("/page")
    public AjaxResult<?> getSysPublicParamPage(PageDomain pageDomain, SysPublicParam sysPublicParam) {
        return AjaxResult.ok(publicParamService.page(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()), Wrappers.query(sysPublicParam)));
    }

    /**
     * 通过id查询公共参数
     *
     * @param publicId id
     * @return R
     */
    @Operation(summary = "通过id查询公共参数")
    @GetMapping("/{publicId}")
    public AjaxResult<?> getById(@PathVariable("publicId") Long publicId) {
        return AjaxResult.ok(publicParamService.getById(publicId));
    }

    /**
     * 新增公共参数
     *
     * @param sysPublicParam 公共参数
     * @return R
     */
    @Operation(summary = "新增公共参数")
    @PostMapping
    public AjaxResult<?> save(@RequestBody SysPublicParam sysPublicParam) {
        if (checkValue(sysPublicParam)) {
            return AjaxResult.failed("设置的值必须被100整除");
        }
        return AjaxResult.ok(publicParamService.save(sysPublicParam));
    }

    /**
     * 修改公共参数
     *
     * @param sysPublicParam 公共参数
     * @return R
     */
    @Operation(summary = "修改公共参数")
    @PutMapping
    public AjaxResult<?> updateById(@RequestBody SysPublicParam sysPublicParam) {
        if (checkValue(sysPublicParam)) {
            return AjaxResult.failed("设置的值必须被100整除");
        }
        return AjaxResult.ok(publicParamService.updateParam(sysPublicParam));
    }

    private boolean checkValue(SysPublicParam sysPublicParam) {
        if ("USE_POINTS_PROPORTION".equals(sysPublicParam.getPublicKey())) {
            try {
                int value = Integer.parseInt(sysPublicParam.getPublicValue());
                if (value % 100 != 0) {
                    return true;
                }
            } catch (Exception e) {
                return true;
            }
        }
        return false;
    }


    /**
     * 通过id删除公共参数
     *
     * @param publicId id
     * @return R
     */
    @Operation(summary = "删除公共参数")
    @DeleteMapping("/{publicId}")
    public AjaxResult<?> removeById(@PathVariable Long publicId) {
        return AjaxResult.ok(publicParamService.removeParam(publicId));
    }

    /**
     * 同步参数
     *
     * @return R
     */
    @PutMapping("/sync")
    public AjaxResult<?> sync() {
        return AjaxResult.ok(publicParamService.syncParamCache());
    }

    @Operation(summary = "更新公共参数")
    @PutMapping("/update/{code}/{value}")
    @CacheEvict(value = "params_details", key = "#code")
    public AjaxResult<?> updateParams(@PathVariable("code") String code, @PathVariable("value") String value) {
        return AjaxResult.ok(publicParamService.update(Wrappers.<SysPublicParam>lambdaUpdate().eq(SysPublicParam::getPublicKey, code).set(SysPublicParam::getPublicValue, value)));
    }
}
