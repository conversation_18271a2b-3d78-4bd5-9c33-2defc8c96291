package com.coocare.ai.controller;


import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.entity.AiAgent;
import com.coocare.ai.service.AiAgentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description:
 * @author: Adam
 * @create: 2025-07-31 10:45
 **/

@Tag(name = "智能体管理")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/sys/agent")
public class AgentController {

    private final AiAgentService aiAgentService;

    /**
     * 获取所有可用的智能体列表
     * @return 智能体列表
     */
    @Operation(summary = "获取所有可用的智能体列表", description = "获取系统中所有可用的智能体列表")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "500", description = "获取失败")
    })
    @GetMapping("/agents/all")
    public AjaxResult<List<AiAgent>> getAllAvailableAgents() {
        try {
            List<AiAgent> agents = aiAgentService.getAllAvailableAgents();
            return AjaxResult.ok(agents);
        } catch (Exception e) {
            log.error("获取所有可用智能体失败", e);
            return AjaxResult.failed("获取智能体列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取已启用的智能体ID列表
     * @return 已启用的智能体ID列表
     */
    @Operation(summary = "获取已启用的智能体ID列表", description = "获取系统中已启用的智能体ID列表")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "500", description = "获取失败")
    })
    @GetMapping("/agents/enabled")
    public AjaxResult<List<Long>> getEnabledAgents() {
        try {
            List<Long> enabledAgents = aiAgentService.getEnabledAgents();
            return AjaxResult.ok(enabledAgents);
        } catch (Exception e) {
            log.error("获取已启用智能体列表失败", e);
            return AjaxResult.failed("获取已启用智能体列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取已启用的智能体详细信息列表
     * @return 已启用的智能体详细信息列表
     */
    @Operation(summary = "获取已启用的智能体详细信息", description = "获取系统中已启用的智能体详细信息列表")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "500", description = "获取失败")
    })
    @GetMapping("/agents/enabled/details")
    public AjaxResult<List<AiAgent>> getEnabledAgentDetails() {
        try {
            List<AiAgent> enabledAgents = aiAgentService.getEnabledAgentDetails();
            return AjaxResult.ok(enabledAgents);
        } catch (Exception e) {
            log.error("获取已启用智能体详细信息失败", e);
            return AjaxResult.failed("获取已启用智能体详细信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取默认智能体
     * @return 默认智能体名称
     */
    @Operation(summary = "获取默认智能体", description = "获取系统配置的默认智能体")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "500", description = "获取失败")
    })
    @GetMapping("/agents/default")
    public AjaxResult<String> getDefaultAgent() {
        try {
            String defaultAgent = aiAgentService.getDefaultAgent();
            return AjaxResult.ok(defaultAgent);
        } catch (Exception e) {
            log.error("获取默认智能体失败", e);
            return AjaxResult.failed("获取默认智能体失败: " + e.getMessage());
        }
    }
}
