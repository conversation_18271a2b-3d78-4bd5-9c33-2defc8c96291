package com.coocare.ai.controller;


import cn.dev33.satoken.annotation.SaIgnore;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.config.oss.OssProperties;
import com.coocare.ai.config.oss.OssTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 智能体后端服务控制器
 * @author: Adam
 * @create: 2025-04-16 11:31
 **/

@SaIgnore
@Tag(name = "智能体后端服务控制器")
@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/sys/backend")
public class ServerController {

    /**
     * 获取所有应用
     * 初始化应用API密钥
     * 获取API秘钥
     *
     */

//    private final DifyChat difyChat;
    private final OssProperties ossProperties;
    private final OssTemplate ossTemplate;

    @Operation(summary = "文件上传接口", description = "用于上传文件到服务器")
    @GetMapping("fileUpload")
    public AjaxResult<?> fileUpload(){

        return AjaxResult.ok(ossTemplate.getObject(ossProperties.getBucketName(), "123.jpeg"));
    }

}
