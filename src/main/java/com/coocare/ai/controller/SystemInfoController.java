package com.coocare.ai.controller;

import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.entity.sys.dto.SystemInfoDTO;
import com.coocare.ai.service.ISystemInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 系统信息控制器
 * 用于获取服务器的硬件和系统信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Tag(name = "系统信息管理")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/sys/info")
public class SystemInfoController {

    private final ISystemInfoService systemInfoService;

    /**
     * 获取完整的系统信息
     * @return 系统信息
     */
    @Operation(summary = "获取完整系统信息", description = "获取服务器的CPU、内存、磁盘、网络等完整信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/all")
    public AjaxResult<?> getSystemInfo() {
        try {
            SystemInfoDTO systemInfo = systemInfoService.getSystemInfo();
            return AjaxResult.ok(systemInfo);
        } catch (Exception e) {
            log.error("获取系统信息失败", e);
            return AjaxResult.failed("获取系统信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取CPU信息
     * @return CPU信息
     */
    @Operation(summary = "获取CPU信息", description = "获取服务器CPU的详细信息，包括型号、核心数、使用率等")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/cpu")
    public AjaxResult<?> getCpuInfo() {
        try {
            SystemInfoDTO.CpuInfo cpuInfo = systemInfoService.getCpuInfo();
            return AjaxResult.ok(cpuInfo);
        } catch (Exception e) {
            log.error("获取CPU信息失败", e);
            return AjaxResult.failed("获取CPU信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取内存信息
     * @return 内存信息
     */
    @Operation(summary = "获取内存信息", description = "获取服务器内存的详细信息，包括总容量、已使用、可用内存等")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/memory")
    public AjaxResult<?> getMemoryInfo() {
        try {
            SystemInfoDTO.MemoryInfo memoryInfo = systemInfoService.getMemoryInfo();
            return AjaxResult.ok(memoryInfo);
        } catch (Exception e) {
            log.error("获取内存信息失败", e);
            return AjaxResult.failed("获取内存信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取磁盘信息
     * @return 磁盘信息列表
     */
    @Operation(summary = "获取磁盘信息", description = "获取服务器所有磁盘的详细信息，包括容量、使用率等")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/disk")
    public AjaxResult<?> getDiskInfo() {
        try {
            List<SystemInfoDTO.DiskInfo> diskInfo = systemInfoService.getDiskInfo();
            return AjaxResult.ok(diskInfo);
        } catch (Exception e) {
            log.error("获取磁盘信息失败", e);
            return AjaxResult.failed("获取磁盘信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取网络信息
     * @return 网络信息列表
     */
    @Operation(summary = "获取网络信息", description = "获取服务器所有网络接口的详细信息，包括MAC地址、IP地址、流量统计等")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/network")
    public AjaxResult<?> getNetworkInfo() {
        try {
            List<SystemInfoDTO.NetworkInfo> networkInfo = systemInfoService.getNetworkInfo();
            return AjaxResult.ok(networkInfo);
        } catch (Exception e) {
            log.error("获取网络信息失败", e);
            return AjaxResult.failed("获取网络信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统基本信息
     * @return 系统基本信息
     */
    @Operation(summary = "获取系统基本信息", description = "获取操作系统、Java版本、时区、主机名等基本信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/basic")
    public AjaxResult<?> getSystemBasicInfo() {
        try {
            SystemInfoDTO.SystemBasicInfo basicInfo = systemInfoService.getSystemBasicInfo();
            return AjaxResult.ok(basicInfo);
        } catch (Exception e) {
            log.error("获取系统基本信息失败", e);
            return AjaxResult.failed("获取系统基本信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取实时网络速度
     * @param interfaceName 网络接口名称，可选
     * @return 网络速度信息
     */
    @Operation(summary = "获取实时网络速度", description = "获取指定网络接口或所有网络接口的实时上传下载速度")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/network/speed")
    public AjaxResult<?> getNetworkSpeed(
            @Parameter(description = "网络接口名称，为空则获取所有接口的速度信息") 
            @RequestParam(required = false) String interfaceName) {
        try {
            List<SystemInfoDTO.NetworkInfo> networkSpeed = systemInfoService.getNetworkSpeed(interfaceName);
            return AjaxResult.ok(networkSpeed);
        } catch (Exception e) {
            log.error("获取网络速度失败", e);
            return AjaxResult.failed("获取网络速度失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统概览信息
     * @return 系统概览
     */
    @Operation(summary = "获取系统概览信息", description = "获取系统的关键指标概览，包括CPU、内存、磁盘使用率等")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/overview")
    public AjaxResult<?> getSystemOverview() {
        try {
            Map<String, Object> overview = new HashMap<>();
            
            // CPU概览
            SystemInfoDTO.CpuInfo cpuInfo = systemInfoService.getCpuInfo();
            Map<String, Object> cpu = new HashMap<>();
            cpu.put("model", cpuInfo.getModel());
            cpu.put("cores", cpuInfo.getCores());
            cpu.put("usage", cpuInfo.getUsage());
            overview.put("cpu", cpu);
            
            // 内存概览
            SystemInfoDTO.MemoryInfo memoryInfo = systemInfoService.getMemoryInfo();
            Map<String, Object> memory = new HashMap<>();
            memory.put("total", memoryInfo.getTotalFormatted());
            memory.put("used", memoryInfo.getUsedFormatted());
            memory.put("usage", memoryInfo.getUsage());
            overview.put("memory", memory);
            
            // 磁盘概览
            List<SystemInfoDTO.DiskInfo> diskInfoList = systemInfoService.getDiskInfo();
            double totalDiskUsage = diskInfoList.stream()
                    .filter(disk -> disk.getTotal() > 0)
                    .mapToDouble(SystemInfoDTO.DiskInfo::getUsage)
                    .average()
                    .orElse(0.0);
            Map<String, Object> disk = new HashMap<>();
            disk.put("count", diskInfoList.size());
            disk.put("averageUsage", Math.round(totalDiskUsage * 100.0) / 100.0);
            overview.put("disk", disk);
            
            // 系统基本信息
            SystemInfoDTO.SystemBasicInfo basicInfo = systemInfoService.getSystemBasicInfo();
            Map<String, Object> system = new HashMap<>();
            system.put("os", basicInfo.getOsName() + " " + basicInfo.getOsVersion());
            system.put("uptime", basicInfo.getUptime());
            system.put("timeZone", basicInfo.getTimeZone());
            system.put("hostname", basicInfo.getHostname());
            overview.put("system", system);
            
            return AjaxResult.ok(overview);
        } catch (Exception e) {
            log.error("获取系统概览失败", e);
            return AjaxResult.failed("获取系统概览失败: " + e.getMessage());
        }
    }

    /**
     * 获取MAC地址列表
     * @return MAC地址列表
     */
    @Operation(summary = "获取MAC地址列表", description = "获取服务器所有网络接口的MAC地址")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/mac")
    public AjaxResult<?> getMacAddresses() {
        try {
            List<SystemInfoDTO.NetworkInfo> networkInfo = systemInfoService.getNetworkInfo();
            Map<String, String> macAddresses = new HashMap<>();
            
            for (SystemInfoDTO.NetworkInfo network : networkInfo) {
                if (network.getMacAddress() != null && !network.getMacAddress().isEmpty()) {
                    macAddresses.put(network.getName(), network.getMacAddress());
                }
            }
            
            return AjaxResult.ok(macAddresses);
        } catch (Exception e) {
            log.error("获取MAC地址失败", e);
            return AjaxResult.failed("获取MAC地址失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前时区信息
     * @return 时区信息
     */
    @Operation(summary = "获取当前时区信息", description = "获取服务器当前的时区设置和时间信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/timezone")
    public AjaxResult<?> getTimezoneInfo() {
        try {
            SystemInfoDTO.SystemBasicInfo basicInfo = systemInfoService.getSystemBasicInfo();
            Map<String, Object> timezoneInfo = new HashMap<>();
            timezoneInfo.put("timeZone", basicInfo.getTimeZone());
            timezoneInfo.put("currentTime", basicInfo.getCurrentTime());
            timezoneInfo.put("bootTime", basicInfo.getBootTime());
            timezoneInfo.put("uptime", basicInfo.getUptime());
            
            return AjaxResult.ok(timezoneInfo);
        } catch (Exception e) {
            log.error("获取时区信息失败", e);
            return AjaxResult.failed("获取时区信息失败: " + e.getMessage());
        }
    }

    /**
     * 关闭系统
     * @return 操作结果
     */
    @Operation(summary = "关闭系统", description = "立即关闭服务器系统")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "关机命令已发送"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/shutdown")
    public AjaxResult<?> shutdownSystem() {
        try {
            boolean result = systemInfoService.shutdownSystem();
            if (result) {
                return AjaxResult.ok(null, "系统关机命令已发送");
            } else {
                return AjaxResult.failed("系统关机命令执行失败");
            }
        } catch (Exception e) {
            log.error("执行关机操作失败", e);
            return AjaxResult.failed("执行关机操作失败: " + e.getMessage());
        }
    }

    /**
     * 重启系统
     * @return 操作结果
     */
    @Operation(summary = "重启系统", description = "立即重启服务器系统")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "重启命令已发送"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/restart")
    public AjaxResult<?> restartSystem() {
        try {
            boolean result = systemInfoService.restartSystem();
            if (result) {
                return AjaxResult.ok(null, "系统重启命令已发送");
            } else {
                return AjaxResult.failed("系统重启命令执行失败");
            }
        } catch (Exception e) {
            log.error("执行重启操作失败", e);
            return AjaxResult.failed("执行重启操作失败: " + e.getMessage());
        }
    }
}