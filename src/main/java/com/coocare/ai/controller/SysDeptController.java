package com.coocare.ai.controller;

import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.config.plugins.logging.annotation.Logging;
import com.coocare.ai.entity.sys.SysDept;
import com.coocare.ai.service.ISysDeptService;
import com.coocare.ai.utils.PageDomain;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 部门管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Tag(name = "部门管理")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/sys/dept")
public class SysDeptController {

    private final ISysDeptService sysDeptService;

    /**
     * 获取部门树形结构
     * @param parentId 父部门ID
     * @param name 部门名称
     * @return 树形结构
     */
    @Operation(summary = "获取部门树形结构")
    @GetMapping("/tree")
    public AjaxResult<?> treeDept(Long parentId, String name) {
        return AjaxResult.ok(sysDeptService.treeDept(parentId, name));
    }

    /**
     * 分页查询部门信息
     * @param pageDomain 分页参数
     * @param name 部门名称
     * @return 分页结果
     */
    @Operation(summary = "分页查询部门信息")
    @GetMapping("/page")
    public AjaxResult<?> pageInfo(PageDomain pageDomain, String name) {
        return AjaxResult.ok(sysDeptService.pageInfo(pageDomain, name));
    }

    /**
     * 根据ID查询部门详细信息
     * @param id 部门ID
     * @return 部门详细信息
     */
    @Operation(summary = "根据ID查询部门详细信息")
    @GetMapping("/{id}")
    public AjaxResult<?> getById(@PathVariable Long id) {
        return AjaxResult.ok(sysDeptService.getById(id));
    }

    /**
     * 根据父部门ID获取子部门列表
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    @Operation(summary = "根据父部门ID获取子部门列表")
    @GetMapping("/children/{parentId}")
    public AjaxResult<?> getChildrenByParentId(@PathVariable Long parentId) {
        return AjaxResult.ok(sysDeptService.getChildrenByParentId(parentId));
    }

    /**
     * 新增部门
     * @param sysDept 部门信息
     * @return success/false
     */
    @Operation(summary = "新增部门")
    @Logging("新增部门")
    @PostMapping
    public AjaxResult<?> save(@Valid @RequestBody SysDept sysDept) {
        // 如果没有设置父部门ID，默认设置为0（根部门）
        if (sysDept.getParentId() == null) {
            sysDept.setParentId(0L);
        }
        return AjaxResult.ok(sysDeptService.save(sysDept));
    }

    /**
     * 修改部门
     * @param sysDept 部门信息
     * @return success/false
     */
    @Operation(summary = "修改部门")
    @Logging("修改部门")
    @PutMapping
    public AjaxResult<?> update(@Valid @RequestBody SysDept sysDept) {
        // 检查是否将部门设置为自己的子部门
        if (sysDept.getDeptId().equals(sysDept.getParentId())) {
            return AjaxResult.failed("不能将部门设置为自己的父部门");
        }
        return AjaxResult.ok(sysDeptService.updateById(sysDept));
    }

    /**
     * 删除部门
     * @param id 部门ID
     * @return success/false
     */
    @Operation(summary = "删除部门")
    @Logging("删除部门")
    @DeleteMapping("/{id}")
    public AjaxResult<?> removeById(@PathVariable Long id) {
        // 检查是否有子部门
        if (sysDeptService.hasChildren(id)) {
            return AjaxResult.failed("该部门下还有子部门，不能删除");
        }
        
        // 这里可以添加检查是否有用户关联的逻辑
        // 暂时使用逻辑删除
        SysDept dept = new SysDept();
        dept.setDeptId(id);
        dept.setDelFlag("1");
        return AjaxResult.ok(sysDeptService.updateById(dept));
    }

    /**
     * 批量删除部门
     * @param ids 部门ID数组
     * @return success/false
     */
    @Operation(summary = "批量删除部门")
    @Logging("批量删除部门")
    @DeleteMapping
    public AjaxResult<?> removeByIds(@RequestBody Long[] ids) {
        for (Long id : ids) {
            if (sysDeptService.hasChildren(id)) {
                return AjaxResult.failed("部门ID为 " + id + " 的部门下还有子部门，不能删除");
            }
        }
        
        // 批量逻辑删除
        for (Long id : ids) {
            SysDept dept = new SysDept();
            dept.setDeptId(id);
            dept.setDelFlag("1");
            sysDeptService.updateById(dept);
        }
        return AjaxResult.ok();
    }

    /**
     * 获取所有部门列表（不分页）
     * @return 部门列表
     */
    @Operation(summary = "获取所有部门列表")
    @GetMapping("/list")
    public AjaxResult<?> listAll() {
        return AjaxResult.ok(sysDeptService.list());
    }

}
