package com.coocare.ai.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import io.github.guoshiqiufeng.dify.core.pojo.DifyPageResult;
import io.github.guoshiqiufeng.dify.dataset.DifyDataset;
import io.github.guoshiqiufeng.dify.dataset.dto.request.*;
import io.github.guoshiqiufeng.dify.dataset.dto.request.document.CustomRule;
import io.github.guoshiqiufeng.dify.dataset.dto.request.document.PreProcessingRule;
import io.github.guoshiqiufeng.dify.dataset.dto.request.document.ProcessRule;
import io.github.guoshiqiufeng.dify.dataset.dto.response.DatasetResponse;
import io.github.guoshiqiufeng.dify.dataset.dto.response.DocumentCreateResponse;
import io.github.guoshiqiufeng.dify.dataset.dto.response.DocumentInfo;
import io.github.guoshiqiufeng.dify.dataset.dto.response.UploadFileInfoResponse;
import io.github.guoshiqiufeng.dify.dataset.enums.IndexingTechniqueEnum;
import io.github.guoshiqiufeng.dify.dataset.enums.document.DocFormEnum;
import io.github.guoshiqiufeng.dify.dataset.enums.document.DocTypeEnum;
import io.github.guoshiqiufeng.dify.dataset.enums.document.ModeEnum;
import io.github.guoshiqiufeng.dify.dataset.enums.document.PreProcessingRuleTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @description: 知识库接口
 * @author: Adam
 * @create: 2025-04-15 15:48
 **/
@SaIgnore
@Tag(name = "知识库接口")
@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/sys/dataset")
public class DatasetController {

    private final DifyDataset dataset;

    /**
     * 分页获取知识库列表
     * @param pageDomain 分页参数
     * @return 知识库分页列表
     */
    @Operation(summary = "分页获取知识库列表", description = "获取系统中所有知识库的分页列表")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("page")
    public AjaxResult<?> queryDatasetPage(
            @Parameter(description = "分页参数") PageDomain pageDomain) {
        DatasetPageRequest datasetPageRequest = new DatasetPageRequest();
        datasetPageRequest.setPage(pageDomain.getPageNo());
        datasetPageRequest.setLimit(pageDomain.getPageSize());
        try {
            DifyPageResult<DatasetResponse> res = dataset.page(datasetPageRequest);
            return AjaxResult.ok(new PageUtils(res.getData(), res.getTotal(), res.getLimit(), res.getPage()));
        } catch (Exception e) {
            log.error("获取知识库列表失败", e);
            return AjaxResult.failed();
        }
    }

    /**
     * 分页查询知识库文档列表
     * @param datasetId 知识库ID
     * @param keyword 搜索关键词
     * @param pageDomain 分页参数
     * @return 文档分页列表
     */
    @Operation(summary = "分页查询知识库文档列表", description = "根据知识库ID和关键词分页查询文档信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "404", description = "知识库不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("pageDocument/{datasetId}")
    public AjaxResult<?> queryDatasetDocument(
            @Parameter(description = "知识库ID", required = true) @PathVariable("datasetId") String datasetId,
            @Parameter(description = "搜索关键词，支持文档名称模糊搜索") @RequestParam(value = "keyword", required = false) String keyword,
            @Parameter(description = "分页参数") PageDomain pageDomain) {
        DatasetPageDocumentRequest datasetPageDocumentRequest = new DatasetPageDocumentRequest();
        datasetPageDocumentRequest.setDatasetId(datasetId);
        datasetPageDocumentRequest.setKeyword(keyword);
        datasetPageDocumentRequest.setPage(pageDomain.getPageNo());
        datasetPageDocumentRequest.setLimit(pageDomain.getPageSize());
        try {
            DifyPageResult<DocumentInfo> res = dataset.pageDocument(datasetPageDocumentRequest);
            return AjaxResult.ok(new PageUtils(res.getData(), res.getTotal(), res.getLimit(), res.getPage()));
        } catch (Exception e) {
            log.error("分页查询文档列表失败", e);
            return AjaxResult.failed();
        }
    }

    @Operation(summary = "获取上传文件信息")
    @GetMapping("getDocumentInfo/{datasetId}/{documentId}")
    public AjaxResult<?> getDocumentInfo(@PathVariable("datasetId") String datasetId,
                                         @PathVariable("documentId") String documentId) {
        try {
            UploadFileInfoResponse fileInfo = dataset.uploadFileInfo(datasetId, documentId);
            return AjaxResult.ok(fileInfo);
        } catch (Exception e) {
            log.error("获取上传文件信息失败", e);
            return AjaxResult.failed();
        }
    }

//    @Operation(summary = "删除文档")
//    @DeleteMapping("removeDocument/{datasetId}/{documentId}")
//    public AjaxResult<?> removeDocument(@PathVariable("datasetId") String datasetId,
//                                        @PathVariable("documentId") String documentId) {
//        try {
//            DocumentDeleteResponse response = dataset.deleteDocument(datasetId, documentId);
//            if (response.getResult().equalsIgnoreCase("success")) {
//                return AjaxResult.ok();
//            } else {
//                return AjaxResult.failed();
//            }
//        } catch (Exception e) {
//            log.error("删除文档失败", e);
//            return AjaxResult.failed();
//        }
//    }

    @Operation(summary = "创建文档")
    @PostMapping(path = "createDocument/{datasetId}", consumes = "multipart/form-data")
    public AjaxResult<?> createDocument(@PathVariable("datasetId") String datasetId,
                                        @RequestParam("file") MultipartFile file,
                                        @Parameter(description = "文档形式 默认情况使用 hierarchical_model, QA格式使用 qa_model") @RequestParam(value = "docForm", defaultValue = "hierarchical_model") DocFormEnum docForm,
                                        @Parameter(description = "是否屏蔽url") @RequestParam(value = "removeUrl") boolean removeUrl) {

        try {
            DocumentCreateByFileRequest documentCreateByFileRequest = new DocumentCreateByFileRequest();
            documentCreateByFileRequest.setDatasetId(datasetId);
            documentCreateByFileRequest.setFile(file);
            documentCreateByFileRequest.setDocType(DocTypeEnum.others);
            documentCreateByFileRequest.setIndexingTechnique(IndexingTechniqueEnum.HIGH_QUALITY);
            documentCreateByFileRequest.setDocForm(docForm);
            documentCreateByFileRequest.setDocLanguage("Chinese");
            ProcessRule processRule = new ProcessRule();
            processRule.setMode(ModeEnum.automatic);
            if (removeUrl) {
                processRule.setMode(ModeEnum.custom);
                CustomRule customRule = new CustomRule();
                PreProcessingRule preProcessingRule = new PreProcessingRule(PreProcessingRuleTypeEnum.remove_urls_emails, Boolean.TRUE);
                customRule.setPreProcessingRules(List.of(preProcessingRule));
                processRule.setRules(customRule);
            }
            documentCreateByFileRequest.setProcessRule(processRule);
            /**
             * 首次调用
             */
//            RetrievalModel retrievalModel = new RetrievalModel();
//            retrievalModel.setSearchMethod(SearchMethodEnum.hybrid_search);
//            retrievalModel.setRerankingEnable(false);
//            retrievalModel.setTopK(2);
//            retrievalModel.setScoreThresholdEnabled(true);
//            retrievalModel.setScoreThreshold(Float.valueOf("0.3"));
//            documentCreateByFileRequest.setRetrievalModel(retrievalModel);
//            documentCreateByFileRequest.setEmbeddingModel("bge-m3:latest");
//            documentCreateByFileRequest.setEmbeddingModelProvider("langgenius/ollama/ollama");

            DocumentCreateResponse response = dataset.createDocumentByFile(documentCreateByFileRequest);
            return AjaxResult.ok(response);
        } catch (Exception e) {
            log.error("创建文档失败", e);
            return AjaxResult.failed();
        }
    }

    @Operation(summary = "更新文档")
    @PostMapping(path = "createDocument/{datasetId}/{documentId}", consumes = "multipart/form-data")
    public AjaxResult<?> updateDocument(@PathVariable("datasetId") String datasetId,
                                        @PathVariable("documentId") String documentId,
                                        @RequestParam("file") MultipartFile file,
                                        @Parameter(description = "文档形式 默认情况使用 hierarchical_model, QA格式使用 qa_model") @RequestParam(value = "docForm", defaultValue = "hierarchical_model") DocFormEnum docForm) {
        try {
            DocumentUpdateByFileRequest request = new DocumentUpdateByFileRequest();
            request.setDatasetId(datasetId);
            request.setFile(file);
            request.setDocumentId(documentId);
            request.setDocType(DocTypeEnum.others);
            request.setIndexingTechnique(IndexingTechniqueEnum.HIGH_QUALITY);
            request.setDocForm(docForm);
            request.setDocLanguage("Chinese");
            DocumentCreateResponse response = dataset.updateDocumentByFile(request);
            return AjaxResult.ok(response);
        } catch (Exception e) {
            log.error("更新文档失败", e);
            return AjaxResult.failed();
        }
    }


    @Operation(summary = "查询文档索引状态-于上传/更新后使用")
    @GetMapping("getDocumentIndexingStatus/{datasetId}/{batch}")
    public AjaxResult<?> getDocumentIndexingStatus(@PathVariable("datasetId") String datasetId,
                                                   @PathVariable("batch") String batch) {
        try {
            DocumentIndexingStatusRequest documentIndexingStatusRequest = new DocumentIndexingStatusRequest();
            documentIndexingStatusRequest.setDatasetId(datasetId);
            documentIndexingStatusRequest.setBatch(batch);
            return AjaxResult.ok(dataset.indexingStatus(documentIndexingStatusRequest));
        } catch (Exception e) {
            log.error("查询文档索引状态失败", e);
            return AjaxResult.failed();
        }
    }


    @Operation(summary = "数据检索/召回测试")
    @GetMapping("retrieve/{datasetId}")
    public AjaxResult<?> retrieve(@PathVariable("datasetId") String datasetId,
                                  @RequestParam("query") String query) {
        try {
            RetrieveRequest request = new RetrieveRequest();
            request.setDatasetId(datasetId);
            request.setQuery(query);
            return AjaxResult.ok(dataset.retrieve(request));
        } catch (Exception e) {
            log.error("数据检索/召回测试失败", e);
            return AjaxResult.failed();
        }
    }


}
