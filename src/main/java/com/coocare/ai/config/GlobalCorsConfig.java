package com.coocare.ai.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import java.util.Collections;

/**
 * Created with IntelliJ IDEA.
 *
 * <AUTHOR>
 * @create 2020/4/10 9:31 上午
 */

@Configuration
public class GlobalCorsConfig {

    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();

        //1,允许任何来源
        config.setAllowedOriginPatterns(Collections.singletonList("*"));
        //2,允许任何请求头
        config.addAllowedHeader(CorsConfiguration.ALL);
        //3,允许任何方法
        config.addAllowedMethod(CorsConfiguration.ALL);
        //4,允许凭证
        config.setAllowCredentials(true);

        UrlBasedCorsConfigurationSource configSource = new UrlBasedCorsConfigurationSource();
        configSource.registerCorsConfiguration("/**", config);
        return new CorsFilter(configSource);
    }

}
