package com.coocare.ai.config.mail;

import com.coocare.ai.service.SysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.util.Properties;

/**
 * 邮件配置类
 * <AUTHOR>
 */
@Configuration
@RequiredArgsConstructor
@Slf4j
public class MailConfig implements ApplicationRunner {

    private final SysConfigService sysConfigService;
    private JavaMailSenderImpl mailSender;

    @Bean
    public JavaMailSender javaMailSender() {
        this.mailSender = new JavaMailSenderImpl();
        return this.mailSender;
    }

    @Override
    public void run(ApplicationArguments args) {
        initMailConfig();
    }

    /**
     * 初始化邮件配置
     */
    public void initMailConfig() {
        try {
            String host = sysConfigService.getValue("MAIL_HOST");
            String port = sysConfigService.getValue("MAIL_PORT");
            String username = sysConfigService.getValue("MAIL_USERNAME");
            String password = sysConfigService.getValue("MAIL_PASSWORD");
            String protocol = sysConfigService.getValue("MAIL_PROTOCOL");
            String auth = sysConfigService.getValue("MAIL_AUTH");
            String starttls = sysConfigService.getValue("MAIL_STARTTLS");

            if (host != null && port != null && username != null && password != null) {
                mailSender.setHost(host);
                mailSender.setPort(Integer.parseInt(port));
                mailSender.setUsername(username);
                mailSender.setPassword(password);
                mailSender.setProtocol(protocol != null ? protocol : "smtp");

                Properties props = mailSender.getJavaMailProperties();
                props.put("mail.transport.protocol", protocol != null ? protocol : "smtp");
                props.put("mail.smtp.auth", auth != null ? auth : "true");
                props.put("mail.smtp.starttls.enable", starttls != null ? starttls : "true");
                props.put("mail.debug", "false");

                log.info("邮件配置初始化成功: host={}, port={}, username={}", host, port, username);
            } else {
                log.warn("邮件配置不完整，请检查数据库配置");
            }
        } catch (Exception e) {
            log.error("邮件配置初始化失败", e);
        }
    }

    /**
     * 重新加载邮件配置
     */
    public void reloadMailConfig() {
        initMailConfig();
    }
}