package com.coocare.ai.config.constants;


import com.coocare.ai.enums.LanguageEnum;

/**
 * Created with IntelliJ IDEA.
 *
 * <AUTHOR>
 * @create 2024/4/22 上午11:04
 */

public interface CommonConstants {

    String UNDERLINE = "_";

    /** 默认语言 */
    LanguageEnum DEFAULT_LANG = LanguageEnum.LANGUAGE_ZH_CN;

    /**
     * header 中租户ID
     */
    String TENANT_ID = "TENANT-ID";

    Long TENANT_ID_1 = 1L;

    /**
     * 菜单树根节点
     */
    Long MENU_TREE_ROOT_ID = -1L;

    /**
     * 客户端允许同时在线数量
     */
    String ONLINE_QUANTITY = "online_quantity";

    /**
     * 请求开始时间
     */
    String REQUEST_START_TIME = "REQUEST-START-TIME";

    /**
     * 当前页
     */
    String CURRENT = "current";

    /**
     * size
     */
    String SIZE = "size";

}
