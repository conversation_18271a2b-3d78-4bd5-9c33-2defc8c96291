package com.coocare.ai.config.domain;

import com.coocare.ai.config.i18n.I18nMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 响应信息主体
 *
 * @param <T>
 * <AUTHOR>
 */
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Schema(description = "响应信息主体")
public class AjaxResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Getter
    @Setter
    @Schema(description = "返回标记：成功标记=0，失败标记=1")
    private int code;

    @Getter
    @Setter
    @Schema(description = "返回信息")
    private String msg;

    @Getter
    @Setter
    @Schema(description = "数据")
    private T data;

    @Getter
    @Setter
    private String result;

    public static <T> AjaxResult<T> ok() {
        return restResult(null, 0, I18nMessage.getMessage("system.ok"), null);
    }

    public static <T> AjaxResult<T> ok(T data) {
        return restResult(data, 0, I18nMessage.getMessage("system.ok"), null);
    }

    public static <T> AjaxResult<T> ok(T data, String msg) {
        return restResult(data, 0, I18nMessage.getMessage(msg), null);
    }

    public static <T> AjaxResult<T> success() {
        return restResult(null, 0, I18nMessage.getMessage("system.ok"), null);
    }

    public static <T> AjaxResult<T> success(T data) {
        return restResult(data, 0, I18nMessage.getMessage("system.ok"), null);
    }

    public static <T> AjaxResult<T> success(T data, String msg) {
        return restResult(data, 0, I18nMessage.getMessage(msg), null);
    }

    public static <T> AjaxResult<T> failed() {
        return restResult(null, -1, I18nMessage.getMessage("system.error"), null);
    }

    public static <T> AjaxResult<T> failed(String msg) {
        return restResult(null, -1, I18nMessage.getMessage(msg), null);
    }

    public static <T> AjaxResult<T> failed(T data) {
        return restResult(data, -1, I18nMessage.getMessage("system.error"), null);
    }

    public static <T> AjaxResult<T> failed(T data, String msg) {
        return restResult(data, -1, I18nMessage.getMessage(msg), null);
    }

    public static <T> AjaxResult<T> failed(Integer code, String msg) {
        return restResult(null, code, I18nMessage.getMessage(msg), null);
    }


    static <T> AjaxResult<T> restResult(T data, int code, String msg, String result) {
        AjaxResult<T> apiResult = new AjaxResult<>();
        apiResult.setCode(code);
        apiResult.setData(data);
        apiResult.setMsg(msg);
        apiResult.setResult(result);
        return apiResult;
    }

}
