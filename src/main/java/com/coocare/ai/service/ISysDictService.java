package com.coocare.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocare.ai.entity.sys.SysDict;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;

import java.util.List;

/**
 * <p>
 * 字典表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
public interface ISysDictService extends IService<SysDict> {

    List<SysDict> getType();

    List<SysDict> getDictList(String dictType);

    PageUtils queryDictPage(SysDict dict, PageDomain pageDomain);

}
