package com.coocare.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocare.ai.entity.sys.SysRole;
import com.coocare.ai.entity.sys.SysUser;
import com.coocare.ai.entity.sys.dto.UserDTO;
import com.coocare.ai.entity.sys.dto.UserInfo;
import com.coocare.ai.entity.sys.dto.UserRoleAssignDTO;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
public interface ISysUserService extends IService<SysUser> {

    Map<String, Object> login(String username, String password);

    PageUtils queryPage(String searchWord, Boolean status, PageDomain pageDomain);

    SysUser queryUserByName(String username);

    Long saveUser(UserDTO userDTO);

    /**
     * 初始化密码
     *
     * @param userId
     * @return
     */
    Boolean resetPassword(Long userId);

    Boolean changeUserPass(String oldPass, String newPass);

    Boolean updateUser(UserDTO userDTO);

    /**
     * 状态切换
     *
     * @param id
     * @param status
     * @return
     */
    Boolean change(Long id, Boolean status);

    /**
     * 分配角色
     *
     * @return
     */
    Object assignRoles(UserRoleAssignDTO dto);

    /**
     * 获取登录用户的信息
     *
     * @param userId
     * @return
     */
    UserInfo userInfo(Long userId);

    /**
     * 获取用户角色
     *
     * @param id
     * @return
     */
    List<SysRole> getRolesByUser(Long id);

    Boolean checkPassword(String password);
}
