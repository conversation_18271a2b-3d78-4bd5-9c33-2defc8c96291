package com.coocare.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocare.ai.entity.sys.SysRole;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;

import java.util.List;

/**
 * <p>
 * 系统角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
public interface ISysRoleService extends IService<SysRole> {

    PageUtils pageInfo(PageDomain pageDomain, String name);

    Object change(Long id, Boolean status);

    Object delById(Long id);

    /**
     * 分配菜单
     */
    Object assignMenu(Long roleId, List<Long> menuIds);

    List<SysRole> getRoleByUid(Long userId);




    /**
     * 通过用户ID，查询角色信息
     * @param userId
     * @return
     */
    List<SysRole> findRolesByUserId(Long userId);

    /**
     * 根据角色ID 查询角色列表
     * @param roleIdList 角色ID列表
     * @param key 缓存key
     * @return
     */
    List<SysRole> findRolesByRoleIds(List<Long> roleIdList, String key);

    /**
     * 通过角色ID，删除角色
     * @param ids
     * @return
     */
    Boolean removeRoleByIds(Long[] ids);

}
