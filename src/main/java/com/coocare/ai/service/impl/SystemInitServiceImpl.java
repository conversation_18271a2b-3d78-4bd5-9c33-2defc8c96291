package com.coocare.ai.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocare.ai.entity.config.CompanyInfo;
import com.coocare.ai.entity.sys.*;
import com.coocare.ai.entity.sys.dto.SystemInitDTO;
import com.coocare.ai.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * <p>
 * 系统初始化服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemInitServiceImpl implements ISystemInitService {

    // 系统初始化标识配置键
    private static final String SYSTEM_INIT_FLAG = "SYSTEM_INITIALIZED";
    private static final String COMPANY_INFO_KEY = "COMPANY_INFO";
    private static final String INDUSTRY_INFO_KEY = "INDUSTRY_INFO";
    private static final String AGENT_CONFIG_KEY = "AGENT_CONFIG";
    private final ISysUserService userService;
    private final ISysRoleService roleService;
    private final ISysDeptService deptService;
    private final SysConfigService configService;
    private final ISysUserRoleService userRoleService;
    private final ISysMenuService menuService;
    private final ISysRoleMenuService roleMenuService;
    private final AiAgentService aiAgentService;

    @Override
    public boolean isSystemInitialized() {
        try {
            String initFlag = configService.getValue(SYSTEM_INIT_FLAG);
            return "true".equals(initFlag);
        } catch (Exception e) {
            log.warn("检查系统初始化状态失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean initializeSystem(SystemInitDTO initDTO) {
        try {
            log.info("开始系统初始化...");

            // 1. 检查系统是否已经初始化
            if (isSystemInitialized()) {
                log.warn("系统已经初始化，跳过初始化流程");
                return false;
            }

            // 2. 创建默认角色和权限
            createDefaultRolesAndPermissions();

            // 3. 创建默认部门
            Long deptId = createDefaultDepartment(initDTO.getCompanyName());

            // 4. 创建管理员账号
            Long adminUserId = createAdminUser(initDTO);

            // 5. 将管理员分配到默认部门
            if (adminUserId != null && deptId != null) {
                SysUser adminUser = userService.getById(adminUserId);
                adminUser.setDeptId(deptId);
                userService.updateById(adminUser);
            }

            // 6. 初始化企业信息
            initCompanyInfo(initDTO);

            // 7. 初始化行业信息
            initIndustryInfo(initDTO);

            // 8. 初始化智能体配置
            initAgentConfig(initDTO);

            // 9. 初始化系统配置
            initSystemConfig(initDTO);

            // 10. 设置系统初始化标识
            SysConfig initConfig = new SysConfig();
            initConfig.setParamKey(SYSTEM_INIT_FLAG);
            initConfig.setParamValue("true");
            initConfig.setRemark("系统初始化完成标识");
            configService.saveOrUpdateSysConfigByKey(initConfig);

            log.info("系统初始化完成");
            return true;

        } catch (Exception e) {
            log.error("系统初始化失败", e);
            throw new RuntimeException("系统初始化失败: " + e.getMessage());
        }
    }

    @Override
    public Long createAdminUser(SystemInitDTO initDTO) {
        try {
            // 检查管理员用户是否已存在
            SysUser existingUser = userService.queryUserByName(initDTO.getAdminUsername());
            if (existingUser != null) {
                log.warn("管理员用户已存在: {}", initDTO.getAdminUsername());
                return existingUser.getUserId();
            }

            // 创建管理员用户
            SysUser adminUser = new SysUser();
            adminUser.setUsername(initDTO.getAdminUsername());
            adminUser.setPassword(BCrypt.hashpw(initDTO.getAdminPassword(), BCrypt.gensalt()));
            adminUser.setName(initDTO.getAdminName());
            adminUser.setNickname(initDTO.getAdminName());
            adminUser.setEmail(initDTO.getAdminEmail());
            adminUser.setPhone(initDTO.getAdminPhone());
            adminUser.setMobile(initDTO.getAdminPhone());
            adminUser.setEnable(true);
            adminUser.setLockFlag("0");
            adminUser.setCreateBy("system");
            adminUser.setCreateTime(LocalDateTime.now());

            userService.save(adminUser);

            // 分配超级管理员角色
            SysRole adminRole = roleService.getOne(
                Wrappers.<SysRole>lambdaQuery().eq(SysRole::getRoleCode, "ROLE_ADMIN")
            );

            if (adminRole != null) {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(adminUser.getUserId());
                userRole.setRoleId(adminRole.getRoleId());
                userRoleService.save(userRole);
            }

            log.info("管理员用户创建成功: {}", initDTO.getAdminUsername());
            return adminUser.getUserId();

        } catch (Exception e) {
            log.error("创建管理员用户失败", e);
            throw new RuntimeException("创建管理员用户失败: " + e.getMessage());
        }
    }

    @Override
    public void initCompanyInfo(SystemInitDTO initDTO) {
        try {
            CompanyInfo companyInfo = new CompanyInfo();
            companyInfo.setCompanyName(initDTO.getCompanyName());
            companyInfo.setSocialCode(initDTO.getTaxNumber());
            companyInfo.setProvince(initDTO.getProvince());
            companyInfo.setCompanyAddress(initDTO.getCompanyAddress());
            companyInfo.setLogo(initDTO.getCompanyLogo());
            companyInfo.setTimeZone(initDTO.getTimeZone());

            SysConfig companyConfig = new SysConfig();
            companyConfig.setParamKey(COMPANY_INFO_KEY);
            companyConfig.setParamValue(JSONUtil.toJsonStr(companyInfo));
            companyConfig.setRemark("企业基本信息配置");
            configService.saveOrUpdateSysConfigByKey(companyConfig);

            // 保存企业联系信息
            saveConfigIfNotEmpty("COMPANY_PHONE", initDTO.getCompanyPhone(), "企业联系电话");
            saveConfigIfNotEmpty("COMPANY_EMAIL", initDTO.getCompanyEmail(), "企业邮箱");

            log.info("企业信息初始化完成");

        } catch (Exception e) {
            log.error("初始化企业信息失败", e);
            throw new RuntimeException("初始化企业信息失败: " + e.getMessage());
        }
    }

    @Override
    public void initIndustryInfo(SystemInitDTO initDTO) {
        try {
            saveConfigIfNotEmpty("COMPANY_INDUSTRY", initDTO.getIndustry(), "所属行业");
            saveConfigIfNotEmpty("COMPANY_INDUSTRY_SUB", initDTO.getIndustrySubcategory(), "行业细分");
            saveConfigIfNotEmpty("COMPANY_SCALE", initDTO.getCompanyScale(), "企业规模");

            log.info("行业信息初始化完成");

        } catch (Exception e) {
            log.error("初始化行业信息失败", e);
            throw new RuntimeException("初始化行业信息失败: " + e.getMessage());
        }
    }

    @Override
    public void initAgentConfig(SystemInitDTO initDTO) {
        try {
            if (ObjectUtil.isNotEmpty(initDTO.getEnabledAgents())) {
                SysConfig agentConfig = new SysConfig();
                agentConfig.setParamKey("ENABLED_AGENTS");
                agentConfig.setParamValue(JSONUtil.toJsonStr(initDTO.getEnabledAgents()));
                agentConfig.setRemark("启用的智能体列表");
                configService.saveOrUpdateSysConfigByKey(agentConfig);
            }

            saveConfigIfNotEmpty("DEFAULT_AGENT", initDTO.getDefaultAgent(), "默认智能体");

            log.info("智能体配置初始化完成");

        } catch (Exception e) {
            log.error("初始化智能体配置失败", e);
            throw new RuntimeException("初始化智能体配置失败: " + e.getMessage());
        }
    }

    @Override
    public void initSystemConfig(SystemInitDTO initDTO) {
        try {
            saveConfigIfNotEmpty("SYSTEM_TIMEZONE", initDTO.getTimeZone(), "系统时区");
            saveConfigIfNotEmpty("SYSTEM_LANGUAGE", initDTO.getLanguage(), "系统语言");
            
            SysConfig emailNotifyConfig = new SysConfig();
            emailNotifyConfig.setParamKey("EMAIL_NOTIFICATION_ENABLED");
            emailNotifyConfig.setParamValue(initDTO.getEnableEmailNotification().toString());
            emailNotifyConfig.setRemark("是否启用邮件通知");
            configService.saveOrUpdateSysConfigByKey(emailNotifyConfig);

            SysConfig smsNotifyConfig = new SysConfig();
            smsNotifyConfig.setParamKey("SMS_NOTIFICATION_ENABLED");
            smsNotifyConfig.setParamValue(initDTO.getEnableSmsNotification().toString());
            smsNotifyConfig.setRemark("是否启用短信通知");
            configService.saveOrUpdateSysConfigByKey(smsNotifyConfig);

            log.info("系统配置初始化完成");

        } catch (Exception e) {
            log.error("初始化系统配置失败", e);
            throw new RuntimeException("初始化系统配置失败: " + e.getMessage());
        }
    }

    @Override
    public void createDefaultRolesAndPermissions() {
        try {
            // 创建超级管理员角色
            createRoleIfNotExists("ROLE_ADMIN", "超级管理员", "系统超级管理员，拥有所有权限");
            
            // 创建普通管理员角色
            createRoleIfNotExists("ROLE_MANAGER", "管理员", "系统管理员，拥有大部分管理权限");
            
            // 创建普通用户角色
            createRoleIfNotExists("ROLE_USER", "普通用户", "系统普通用户，拥有基本操作权限");

            log.info("默认角色创建完成");

        } catch (Exception e) {
            log.error("创建默认角色失败", e);
            throw new RuntimeException("创建默认角色失败: " + e.getMessage());
        }
    }

    @Override
    public Long createDefaultDepartment(String companyName) {
        try {
            // 检查是否已存在根部门
            SysDept existingDept = deptService.getOne(
                Wrappers.<SysDept>lambdaQuery()
                    .eq(SysDept::getParentId, 0L)
                    .eq(SysDept::getDelFlag, "0")
                    .last("LIMIT 1")
            );

            if (existingDept != null) {
                log.info("根部门已存在: {}", existingDept.getName());
                return existingDept.getDeptId();
            }

            // 创建根部门
            SysDept rootDept = new SysDept();
            rootDept.setName(StrUtil.isNotBlank(companyName) ? companyName : "总公司");
            rootDept.setParentId(0L);
            rootDept.setSortOrder(0);
            rootDept.setDelFlag("0");
            rootDept.setCreateBy("system");
            rootDept.setCreateTime(LocalDateTime.now());

            deptService.save(rootDept);

            log.info("默认部门创建完成: {}", rootDept.getName());
            return rootDept.getDeptId();

        } catch (Exception e) {
            log.error("创建默认部门失败", e);
            throw new RuntimeException("创建默认部门失败: " + e.getMessage());
        }
    }

    /**
     * 创建角色（如果不存在）
     */
    private void createRoleIfNotExists(String roleCode, String roleName, String roleDesc) {
        SysRole existingRole = roleService.getOne(
            Wrappers.<SysRole>lambdaQuery().eq(SysRole::getRoleCode, roleCode)
        );

        if (existingRole == null) {
            SysRole role = new SysRole();
            role.setRoleCode(roleCode);
            role.setRoleName(roleName);
            role.setRoleDesc(roleDesc);
            role.setEnable(true);
            role.setDelFlag("0");
            role.setCreateBy("system");
            role.setCreateTime(LocalDateTime.now());
            roleService.save(role);
            log.info("角色创建成功: {}", roleName);
        }
    }

    /**
     * 保存配置（如果值不为空）
     */
    private void saveConfigIfNotEmpty(String key, String value, String remark) {
        if (StrUtil.isNotBlank(value)) {
            SysConfig config = new SysConfig();
            config.setParamKey(key);
            config.setParamValue(value);
            config.setRemark(remark);
            configService.saveOrUpdateSysConfigByKey(config);
        }
    }

}
