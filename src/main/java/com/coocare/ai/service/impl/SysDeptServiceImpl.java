package com.coocare.ai.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.entity.sys.SysDept;
import com.coocare.ai.mapper.SysDeptMapper;
import com.coocare.ai.service.ISysDeptService;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 部门管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Service
public class SysDeptServiceImpl extends ServiceImpl<SysDeptMapper, SysDept> implements ISysDeptService {

    @Override
    public List<Tree<Long>> treeDept(Long parentId, String name) {
        LambdaQueryWrapper<SysDept> wrapper = Wrappers.lambdaQuery(SysDept.class)
                .like(StrUtil.isNotBlank(name), SysDept::getName, name)
                .eq(SysDept::getDelFlag, "0")
                .orderByAsc(SysDept::getSortOrder);

        List<SysDept> deptList = this.list(wrapper);

        if (CollUtil.isEmpty(deptList)) {
            return CollUtil.newArrayList();
        }

        return TreeUtil.build(deptList, parentId, TreeNodeConfig.DEFAULT_CONFIG.setIdKey("deptId"),
                (treeNode, tree) -> {
                    tree.setId(treeNode.getDeptId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setName(treeNode.getName());
                    tree.putExtra("phone", treeNode.getPhone());
                    tree.putExtra("email", treeNode.getEmail());
                    tree.putExtra("groupEmail", treeNode.getGroupEmail());
                    tree.putExtra("officePhone", treeNode.getOfficePhone());
                    tree.putExtra("sortOrder", treeNode.getSortOrder());
                    tree.putExtra("createTime", treeNode.getCreateTime());
                    tree.putExtra("updateTime", treeNode.getUpdateTime());
                });
    }

    @Override
    public PageUtils pageInfo(PageDomain pageDomain, String name) {
        LambdaQueryWrapper<SysDept> wrapper = Wrappers.lambdaQuery(SysDept.class)
                .like(StrUtil.isNotBlank(name), SysDept::getName, name)
                .eq(SysDept::getDelFlag, "0")
                .orderByAsc(SysDept::getSortOrder);

        Page<SysDept> page = this.page(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()), wrapper);
        return new PageUtils(page);
    }

    @Override
    public List<SysDept> getChildrenByParentId(Long parentId) {
        LambdaQueryWrapper<SysDept> wrapper = Wrappers.lambdaQuery(SysDept.class)
                .eq(SysDept::getParentId, parentId)
                .eq(SysDept::getDelFlag, "0")
                .orderByAsc(SysDept::getSortOrder);
        return this.list(wrapper);
    }

    @Override
    public boolean hasChildren(Long deptId) {
        LambdaQueryWrapper<SysDept> wrapper = Wrappers.lambdaQuery(SysDept.class)
                .eq(SysDept::getParentId, deptId)
                .eq(SysDept::getDelFlag, "0");
        return this.count(wrapper) > 0;
    }

}
