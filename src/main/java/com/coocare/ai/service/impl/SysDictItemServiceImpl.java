package com.coocare.ai.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocare.ai.entity.sys.SysDict;
import com.coocare.ai.entity.sys.SysDictItem;
import com.coocare.ai.entity.sys.SysMenu;
import com.coocare.ai.mapper.SysDictItemMapper;
import com.coocare.ai.service.ISysDictItemService;
import com.coocare.ai.utils.EmptyUtil;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 字典项 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Service
public class SysDictItemServiceImpl extends MPJBaseServiceImpl<SysDictItemMapper, SysDictItem> implements ISysDictItemService {

    @Override
    @CacheEvict(value = "dict_details", key = "#itemValue")
    public SysDictItem getByItemValue(String itemValue) {
        return getOne(Wrappers.lambdaQuery(SysDictItem.class)
                .eq(SysDictItem::getItemValue, itemValue));
    }

    @Override
    public PageUtils queryDictItemPage(Long dictId, String description, PageDomain pageDomain) {
        return new PageUtils(page(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()), Wrappers.lambdaQuery(SysDictItem.class)
                .eq(SysDictItem::getDictId, dictId)
                .like(EmptyUtil.isNotEmpty(description), SysDictItem::getDescription, description)));
    }

    @Override
    @CacheEvict(value = "dict_items", key = "#dictType")
    public List<SysDictItem> getDictItemList(String dictType) {
        MPJLambdaWrapper<SysDictItem> wrapper = JoinWrappers.lambda(SysDictItem.class)
                .selectAll(SysDictItem.class)
                .leftJoin(SysDict.class, SysDict::getId, SysDictItem::getDictId)
                .eq(SysDictItem::getDictType, dictType)
                .orderByDesc(SysMenu::getSortOrder);
        return selectJoinList(SysDictItem.class, wrapper);
    }
}
