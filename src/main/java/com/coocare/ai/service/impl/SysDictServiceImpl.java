package com.coocare.ai.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.entity.sys.SysDict;
import com.coocare.ai.mapper.SysDictMapper;
import com.coocare.ai.service.ISysDictService;
import com.coocare.ai.utils.EmptyUtil;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Service
public class SysDictServiceImpl extends ServiceImpl<SysDictMapper, SysDict> implements ISysDictService {

    @Override
    public List<SysDict> getType() {
        return list(Wrappers.<SysDict>lambdaQuery().groupBy(SysDict::getDictType));
    }

    @Override
    public List<SysDict> getDictList(String dictType) {
        return list(Wrappers.lambdaQuery(SysDict.class).eq(SysDict::getDictType, dictType));
    }

    @Override
    public PageUtils queryDictPage(SysDict dict, PageDomain pageDomain) {
        return new PageUtils(page(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()), Wrappers.<SysDict>lambdaQuery()
                .eq(EmptyUtil.isNotEmpty(dict.getDictType()), SysDict::getDictType, dict.getDictType())
                .like(EmptyUtil.isNotEmpty(dict.getDescription()), SysDict::getDescription, dict.getDescription())));
    }


}
