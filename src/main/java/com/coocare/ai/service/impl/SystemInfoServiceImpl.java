package com.coocare.ai.service.impl;

import com.coocare.ai.entity.sys.dto.SystemInfoDTO;
import com.coocare.ai.service.ISystemInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import oshi.SystemInfo;
import oshi.hardware.*;
import oshi.software.os.NetworkParams;
import oshi.software.os.OperatingSystem;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.InetAddress;
import java.text.DecimalFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 系统信息服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Slf4j
@Service
public class SystemInfoServiceImpl implements ISystemInfoService {

    private final SystemInfo systemInfo = new SystemInfo();
    private final DecimalFormat df = new DecimalFormat("#.##");

    // 用于计算网络速度的缓存
    private final Map<String, NetworkStats> networkStatsCache = new HashMap<>();

    @Override
    public SystemInfoDTO getSystemInfo() {
        SystemInfoDTO dto = new SystemInfoDTO();
        dto.setCpu(getCpuInfo());
        dto.setMemory(getMemoryInfo());
        dto.setDisks(getDiskInfo());
        dto.setNetworks(getNetworkInfo());
        dto.setSystem(getSystemBasicInfo());
        return dto;
    }

    @Override
    public SystemInfoDTO.CpuInfo getCpuInfo() {
        try {
            CentralProcessor processor = systemInfo.getHardware().getProcessor();
            SystemInfoDTO.CpuInfo cpuInfo = new SystemInfoDTO.CpuInfo();

            cpuInfo.setModel(processor.getProcessorIdentifier().getName());
            cpuInfo.setCores(processor.getPhysicalProcessorCount());
            cpuInfo.setLogicalProcessors(processor.getLogicalProcessorCount());
            cpuInfo.setFrequency(processor.getProcessorIdentifier().getVendorFreq());
            cpuInfo.setArchitecture(processor.getProcessorIdentifier().getMicroarchitecture());

            // 获取CPU使用率
            double cpuUsage = processor.getSystemCpuLoadBetweenTicks(processor.getSystemCpuLoadTicks()) * 100;
            cpuInfo.setUsage(Double.parseDouble(df.format(cpuUsage)));

            // 尝试获取CPU温度
            try {
                Sensors sensors = systemInfo.getHardware().getSensors();
                double temperature = sensors.getCpuTemperature();
                cpuInfo.setTemperature(temperature > 0 ? Double.parseDouble(df.format(temperature)) : 0.0);
            } catch (Exception e) {
                cpuInfo.setTemperature(0.0);
            }

            return cpuInfo;
        } catch (Exception e) {
            log.error("获取CPU信息失败", e);
            return new SystemInfoDTO.CpuInfo();
        }
    }

    @Override
    public SystemInfoDTO.MemoryInfo getMemoryInfo() {
        try {
            GlobalMemory memory = systemInfo.getHardware().getMemory();
            SystemInfoDTO.MemoryInfo memoryInfo = new SystemInfoDTO.MemoryInfo();

            long total = memory.getTotal();
            long available = memory.getAvailable();
            long used = total - available;

            memoryInfo.setTotal(total);
            memoryInfo.setUsed(used);
            memoryInfo.setAvailable(available);
            memoryInfo.setUsage(Double.parseDouble(df.format((double) used / total * 100)));

            memoryInfo.setTotalFormatted(formatBytes(total));
            memoryInfo.setUsedFormatted(formatBytes(used));
            memoryInfo.setAvailableFormatted(formatBytes(available));

            return memoryInfo;
        } catch (Exception e) {
            log.error("获取内存信息失败", e);
            return new SystemInfoDTO.MemoryInfo();
        }
    }

    @Override
    public List<SystemInfoDTO.DiskInfo> getDiskInfo() {
        try {
            List<SystemInfoDTO.DiskInfo> diskInfoList = new ArrayList<>();
            List<HWDiskStore> diskStores = systemInfo.getHardware().getDiskStores();

            for (HWDiskStore disk : diskStores) {
                SystemInfoDTO.DiskInfo diskInfo = new SystemInfoDTO.DiskInfo();
                diskInfo.setName(disk.getName());
                diskInfo.setFileSystem(disk.getModel());

                long total = disk.getSize();
                // 注意：OSHI的HWDiskStore不直接提供已使用空间，需要通过文件系统获取
                diskInfo.setTotal(total);
                diskInfo.setTotalFormatted(formatBytes(total));

                diskInfoList.add(diskInfo);
            }

            // 获取文件系统信息
            OperatingSystem os = systemInfo.getOperatingSystem();
            List<oshi.software.os.OSFileStore> fileStores = os.getFileSystem().getFileStores();

            for (oshi.software.os.OSFileStore fs : fileStores) {
                SystemInfoDTO.DiskInfo diskInfo = new SystemInfoDTO.DiskInfo();
                diskInfo.setName(fs.getName());
                diskInfo.setMountPoint(fs.getMount());
                diskInfo.setFileSystem(fs.getType());

                long total = fs.getTotalSpace();
                long free = fs.getUsableSpace();
                long used = total - free;

                diskInfo.setTotal(total);
                diskInfo.setUsed(used);
                diskInfo.setAvailable(free);
                diskInfo.setUsage(total > 0 ? Double.parseDouble(df.format((double) used / total * 100)) : 0.0);

                diskInfo.setTotalFormatted(formatBytes(total));
                diskInfo.setUsedFormatted(formatBytes(used));
                diskInfo.setAvailableFormatted(formatBytes(free));

                diskInfoList.add(diskInfo);
            }

            return diskInfoList;
        } catch (Exception e) {
            log.error("获取磁盘信息失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<SystemInfoDTO.NetworkInfo> getNetworkInfo() {
        try {
            List<SystemInfoDTO.NetworkInfo> networkInfoList = new ArrayList<>();
            List<NetworkIF> networkIFs = systemInfo.getHardware().getNetworkIFs();

            for (NetworkIF net : networkIFs) {
                SystemInfoDTO.NetworkInfo networkInfo = new SystemInfoDTO.NetworkInfo();
                networkInfo.setName(net.getName());
                networkInfo.setDisplayName(net.getDisplayName());
                networkInfo.setMacAddress(net.getMacaddr());
                networkInfo.setEnabled(net.isKnownVmMacAddr() == false);

                // 获取IP地址
                List<String> ipAddresses = new ArrayList<>();
                String[] ips = net.getIPv4addr();
                if (ips != null) {
                    ipAddresses.addAll(Arrays.asList(ips));
                }
                String[] ipv6s = net.getIPv6addr();
                if (ipv6s != null) {
                    ipAddresses.addAll(Arrays.asList(ipv6s));
                }
                networkInfo.setIpAddresses(ipAddresses);

                // 更新网络统计信息
                net.updateAttributes();
                networkInfo.setBytesReceived(net.getBytesRecv());
                networkInfo.setBytesSent(net.getBytesSent());
                networkInfo.setPacketsReceived(net.getPacketsRecv());
                networkInfo.setPacketsSent(net.getPacketsSent());

                // 计算网络速度
                calculateNetworkSpeed(networkInfo, net);

                networkInfoList.add(networkInfo);
            }

            return networkInfoList;
        } catch (Exception e) {
            log.error("获取网络信息失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public SystemInfoDTO.SystemBasicInfo getSystemBasicInfo() {
        try {
            OperatingSystem os = systemInfo.getOperatingSystem();
            SystemInfoDTO.SystemBasicInfo basicInfo = new SystemInfoDTO.SystemBasicInfo();

            basicInfo.setOsName(os.getFamily());
            basicInfo.setOsVersion(os.getVersionInfo().toString());
            basicInfo.setOsArch(System.getProperty("os.arch"));

            basicInfo.setJavaVersion(System.getProperty("java.version"));
            basicInfo.setJvmName(System.getProperty("java.vm.name"));
            basicInfo.setJvmVersion(System.getProperty("java.vm.version"));

            // 系统启动时间
            long bootTime = os.getSystemBootTime();
            basicInfo.setBootTime(formatDateTime(bootTime));

            // 系统运行时间
            long uptime = System.currentTimeMillis() / 1000 - bootTime;
            basicInfo.setUptime(formatUptime(uptime));

            // 时区和时间
            basicInfo.setTimeZone(TimeZone.getDefault().getID());
            basicInfo.setCurrentTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 主机信息
            try {
                NetworkParams networkParams = os.getNetworkParams();
                basicInfo.setHostname(networkParams.getHostName());
            } catch (Exception e) {
                basicInfo.setHostname(InetAddress.getLocalHost().getHostName());
            }

            basicInfo.setUsername(System.getProperty("user.name"));
            basicInfo.setUserHome(System.getProperty("user.home"));
            basicInfo.setTempDir(System.getProperty("java.io.tmpdir"));

            return basicInfo;
        } catch (Exception e) {
            log.error("获取系统基本信息失败", e);
            return new SystemInfoDTO.SystemBasicInfo();
        }
    }

    @Override
    public List<SystemInfoDTO.NetworkInfo> getNetworkSpeed(String interfaceName) {
        List<SystemInfoDTO.NetworkInfo> result = new ArrayList<>();
        List<NetworkIF> networkIFs = systemInfo.getHardware().getNetworkIFs();

        for (NetworkIF net : networkIFs) {
            if (interfaceName == null || interfaceName.isEmpty() || net.getName().equals(interfaceName)) {
                SystemInfoDTO.NetworkInfo networkInfo = new SystemInfoDTO.NetworkInfo();
                networkInfo.setName(net.getName());
                networkInfo.setDisplayName(net.getDisplayName());

                // 更新网络统计信息
                net.updateAttributes();
                calculateNetworkSpeed(networkInfo, net);

                result.add(networkInfo);
            }
        }

        return result;
    }

    @Override
    public String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.2f %sB", bytes / Math.pow(1024, exp), pre);
    }

    @Override
    public String formatSpeed(long bytesPerSecond) {
        return formatBytes(bytesPerSecond) + "/s";
    }

    /**
     * 计算网络速度
     */
    private void calculateNetworkSpeed(SystemInfoDTO.NetworkInfo networkInfo, NetworkIF net) {
        String interfaceName = net.getName();
        long currentTime = System.currentTimeMillis();
        long currentBytesReceived = net.getBytesRecv();
        long currentBytesSent = net.getBytesSent();

        NetworkStats previousStats = networkStatsCache.get(interfaceName);
        if (previousStats != null) {
            long timeDiff = currentTime - previousStats.timestamp;
            if (timeDiff > 0) {
                long downloadSpeed = (currentBytesReceived - previousStats.bytesReceived) * 1000 / timeDiff;
                long uploadSpeed = (currentBytesSent - previousStats.bytesSent) * 1000 / timeDiff;

                networkInfo.setDownloadSpeed(Math.max(0, downloadSpeed));
                networkInfo.setUploadSpeed(Math.max(0, uploadSpeed));
                networkInfo.setDownloadSpeedFormatted(formatSpeed(Math.max(0, downloadSpeed)));
                networkInfo.setUploadSpeedFormatted(formatSpeed(Math.max(0, uploadSpeed)));
            }
        } else {
            networkInfo.setDownloadSpeed(0);
            networkInfo.setUploadSpeed(0);
            networkInfo.setDownloadSpeedFormatted("0 B/s");
            networkInfo.setUploadSpeedFormatted("0 B/s");
        }

        // 更新缓存
        networkStatsCache.put(interfaceName, new NetworkStats(currentTime, currentBytesReceived, currentBytesSent));
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.systemDefault())
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 格式化运行时间
     */
    private String formatUptime(long seconds) {
        long days = TimeUnit.SECONDS.toDays(seconds);
        long hours = TimeUnit.SECONDS.toHours(seconds) % 24;
        long minutes = TimeUnit.SECONDS.toMinutes(seconds) % 60;
        long secs = seconds % 60;

        return String.format("%d天 %d小时 %d分钟 %d秒", days, hours, minutes, secs);
    }

    /**
     * 网络统计信息缓存类
     */
    private static class NetworkStats {
        final long timestamp;
        final long bytesReceived;
        final long bytesSent;

        NetworkStats(long timestamp, long bytesReceived, long bytesSent) {
            this.timestamp = timestamp;
            this.bytesReceived = bytesReceived;
            this.bytesSent = bytesSent;
        }
    }

    @Override
    public boolean shutdownSystem() {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            Process process;
            
            if (os.contains("win")) {
                // Windows系统
                process = Runtime.getRuntime().exec("shutdown /s /t 1");
            } else if (os.contains("nix") || os.contains("nux") || os.contains("aix")) {
                // Unix/Linux/Mac系统
                process = Runtime.getRuntime().exec("shutdown -h now");
            } else {
                log.error("不支持的操作系统: {}", os);
                return false;
            }
            
            // 等待命令执行完成
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                log.info("系统关机命令执行成功");
                return true;
            } else {
                log.error("系统关机命令执行失败，退出码: {}", exitCode);
                // 读取错误输出
                BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
                String errorLine;
                while ((errorLine = errorReader.readLine()) != null) {
                    log.error("关机命令错误输出: {}", errorLine);
                }
                return false;
            }
        } catch (Exception e) {
            log.error("执行关机操作时发生异常", e);
            return false;
        }
    }

    @Override
    public boolean restartSystem() {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            Process process;
            
            if (os.contains("win")) {
                // Windows系统
                process = Runtime.getRuntime().exec("shutdown /r /t 1");
            } else if (os.contains("nix") || os.contains("nux") || os.contains("aix")) {
                // Unix/Linux/Mac系统
                process = Runtime.getRuntime().exec("shutdown -r now");
            } else {
                log.error("不支持的操作系统: {}", os);
                return false;
            }
            
            // 等待命令执行完成
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                log.info("系统重启命令执行成功");
                return true;
            } else {
                log.error("系统重启命令执行失败，退出码: {}", exitCode);
                // 读取错误输出
                BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
                String errorLine;
                while ((errorLine = errorReader.readLine()) != null) {
                    log.error("重启命令错误输出: {}", errorLine);
                }
                return false;
            }
        } catch (Exception e) {
            log.error("执行重启操作时发生异常", e);
            return false;
        }
    }
}
