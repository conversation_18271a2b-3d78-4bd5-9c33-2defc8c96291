package com.coocare.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocare.ai.entity.sys.SysDictItem;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * <p>
 * 字典项 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
public interface ISysDictItemService extends IService<SysDictItem> {

    SysDictItem getByItemValue(String itemValue);

    PageUtils queryDictItemPage(@PathVariable Long dictId, String description, PageDomain pageDomain);

    List<SysDictItem> getDictItemList(String dictType);

}
