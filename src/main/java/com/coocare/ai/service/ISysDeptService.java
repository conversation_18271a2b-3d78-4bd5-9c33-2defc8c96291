package com.coocare.ai.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocare.ai.entity.sys.SysDept;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;

import java.util.List;

/**
 * <p>
 * 部门管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
public interface ISysDeptService extends IService<SysDept> {

    /**
     * 获取部门树形结构
     * @param parentId 父部门ID
     * @param name 部门名称
     * @return 树形结构
     */
    List<Tree<Long>> treeDept(Long parentId, String name);

    /**
     * 分页查询部门信息
     * @param pageDomain 分页参数
     * @param name 部门名称
     * @return 分页结果
     */
    PageUtils pageInfo(PageDomain pageDomain, String name);

    /**
     * 根据父部门ID获取子部门列表
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<SysDept> getChildrenByParentId(Long parentId);

    /**
     * 检查部门是否有子部门
     * @param deptId 部门ID
     * @return 是否有子部门
     */
    boolean hasChildren(Long deptId);

}
