package com.coocare.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocare.ai.entity.AiAgent;

import java.util.List;

public interface AiAgentService extends IService<AiAgent>{

    /**
     * 获取所有可用的智能体列表
     * @return 智能体列表
     */
    List<AiAgent> getAllAvailableAgents();

    /**
     * 获取已启用的智能体列表
     * @return 已启用的智能体ID列表
     */
    List<Long> getEnabledAgents();

    /**
     * 获取默认智能体
     * @return 默认智能体名称
     */
    String getDefaultAgent();

    /**
     * 获取已启用的智能体详细信息列表
     * @return 已启用的智能体详细信息列表
     */
    List<AiAgent> getEnabledAgentDetails();
}
