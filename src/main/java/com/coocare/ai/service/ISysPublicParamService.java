package com.coocare.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocare.ai.entity.sys.SysPublicParam;

/**
 * <p>
 * 公共参数配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
public interface ISysPublicParamService extends IService<SysPublicParam> {

    /**
     * 通过key查询公共参数指定值
     *
     * @param publicKey
     * @return
     */
    String getSysPublicParamKeyToValue(String publicKey);

    /**
     * 更新参数
     *
     * @param sysPublicParam
     * @return
     */
    boolean updateParam(SysPublicParam sysPublicParam);

    /**
     * 删除参数
     *
     * @param publicId
     * @return
     */
    boolean removeParam(Long publicId);

    /**
     * 同步缓存
     *
     * @return R
     */
    boolean syncParamCache();

}
