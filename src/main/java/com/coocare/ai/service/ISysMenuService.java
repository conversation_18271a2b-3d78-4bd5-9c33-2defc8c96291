package com.coocare.ai.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.entity.sys.SysMenu;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 菜单权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
public interface ISysMenuService extends IService<SysMenu> {
    /**
     * 通过角色编号查询URL 权限
     * @param roleId 角色ID
     * @return 菜单列表
     */
    List<SysMenu> findMenuByRoleId(Long roleId);

    /**
     * 级联删除菜单
     * @param id 菜单ID
     * @return 成功、失败
     */
    AjaxResult<?> removeMenuById(Long id);

    /**
     * 更新菜单信息
     * @param sysMenu 菜单信息
     * @return 成功、失败
     */
    Boolean updateMenuById(SysMenu sysMenu);

    /**
     * 构建树
     * @param parentId 父节点ID
     * @param menuName 菜单名称
     * @return
     */
    List<Tree<Long>> treeMenu(Long parentId, String menuName, String type);

    /**
     * 查询菜单
     * @param voSet
     * @param parentId
     * @return
     */
    List<Tree<Long>> filterMenu(Set<SysMenu> voSet, String type, Long parentId);

}
