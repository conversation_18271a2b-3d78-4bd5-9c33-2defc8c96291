package com.coocare.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocare.ai.entity.sys.SysConfig;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
public interface SysConfigService extends IService<SysConfig> {

    /**
     * 根据key，更新value
     *
     * @param key   参数key
     * @param value 参数value
     */
    void updateValueByKey(String key, String value);

    /**
     * 删除配置信息
     *
     * @param ids 配置项id列表
     */
    void deleteBatch(Long[] ids);

    /**
     * 根据key，获取配置的value值
     *
     * @param key 参数key
     * @return value
     */
    String getValue(String key);


    /**
     * 获取配置信息，并返回对应的类
     *
     * @param key   key
     * @param clazz 类
     * @param <T>   泛型
     * @return 泛型
     */
    <T> T getSysConfigObject(String key, Class<T> clazz);

    /**
     * 删除key的配置信息
     *
     * @param key key
     */
    void removeSysConfig(String key);

    /**
     * 根据是否已经存在以key为名称的配置进行保存或更新
     *
     * @param sysConfig
     */
    void saveOrUpdateSysConfigByKey(SysConfig sysConfig);
}
