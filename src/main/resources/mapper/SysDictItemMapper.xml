<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.SysDictItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.coocare.ai.entity.sys.SysDictItem">
        <id column="id" property="id"/>
        <result column="dict_id" property="dictId"/>
        <result column="item_value" property="itemValue"/>
        <result column="label" property="label"/>
        <result column="dict_type" property="dictType"/>
        <result column="description" property="description"/>
        <result column="scope" property="scope"/>
        <result column="coefficient" property="coefficient"/>
        <result column="readable" property="readable"/>
        <result column="step" property="step"/>
        <result column="remarks" property="remarks"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="history" property="history"/>
    </resultMap>


</mapper>
