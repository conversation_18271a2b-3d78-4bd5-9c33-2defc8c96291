<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.AiAgentMapper">
  <resultMap id="BaseResultMap" type="com.coocare.ai.entity.AiAgent">
    <!--@mbg.generated-->
    <!--@Table ai_agent-->
    <id column="agent_id" jdbcType="BIGINT" property="agentId" />
    <result column="agent_name" jdbcType="VARCHAR" property="agentName" />
    <result column="agent_intro" jdbcType="VARCHAR" property="agentIntro" />
    <result column="enable" jdbcType="TINYINT" property="enable" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    agent_id, agent_name, agent_intro, `enable`
  </sql>
</mapper>