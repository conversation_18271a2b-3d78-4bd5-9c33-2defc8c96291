<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.SysMenuMapper">

    <resultMap id="BaseResultMap" type="com.coocare.ai.entity.sys.SysMenu">
        <id column="menu_id" property="menuId" />
        <result column="name" property="name" />
        <result column="permission" property="permission" />
        <result column="path" property="path" />
        <result column="component" property="component" />
        <result column="frame_src" property="frameSrc" />
        <result column="parent_id" property="parentId" />
        <result column="icon" property="icon" />
        <result column="visible" property="visible" />
        <result column="sort_order" property="sortOrder" />
        <result column="keep_alive" property="keepAlive" />
        <result column="embedded" property="embedded" />
        <result column="meta" property="meta" />
        <result column="menu_type" property="menuType" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <select id="findMenuByRoleId" resultMap="BaseResultMap">
        SELECT
            m.*
        FROM
            sys_menu m
                LEFT JOIN sys_role_menu rm ON m.menu_id = rm.menu_id
        WHERE
            rm.role_id = #{roleId}
          AND m.del_flag = 0
        ORDER BY
            m.sort_order
    </select>
</mapper>
