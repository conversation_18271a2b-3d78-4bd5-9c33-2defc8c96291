<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.SysConfigMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.coocare.ai.entity.sys.SysConfig">
    <id column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="param_key" jdbcType="VARCHAR" property="paramKey" />
    <result column="param_value" jdbcType="LONGVARCHAR" property="paramValue" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
</mapper>