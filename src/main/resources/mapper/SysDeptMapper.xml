<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.SysDeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.coocare.ai.entity.sys.SysDept">
        <id column="dept_id" property="deptId"/>
        <result column="name" property="name"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="group_email" property="groupEmail"/>
        <result column="office_phone" property="officePhone"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="parent_id" property="parentId"/>
    </resultMap>

</mapper>
