<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.SysLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.coocare.ai.entity.sys.SysLog">
        <id column="id" property="id"/>
        <result column="log_type" property="logType"/>
        <result column="title" property="title"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="remote_addr" property="remoteAddr"/>
        <result column="user_agent" property="userAgent"/>
        <result column="request_uri" property="requestUri"/>
        <result column="method" property="method"/>
        <result column="params" property="params"/>
        <result column="time" property="time"/>
        <result column="exception" property="exception"/>
    </resultMap>


</mapper>
