<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.SysRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.coocare.ai.entity.sys.SysRole">
        <id column="role_id" property="roleId"/>
        <result column="role_name" property="roleName"/>
        <result column="role_code" property="roleCode"/>
        <result column="role_desc" property="roleDesc"/>
        <result column="ds_type" property="dsType"/>
        <result column="ds_scope" property="dsScope"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

</mapper>
