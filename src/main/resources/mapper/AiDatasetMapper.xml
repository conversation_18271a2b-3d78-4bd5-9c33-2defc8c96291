<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.AiDatasetMapper">
  <resultMap id="BaseResultMap" type="com.coocare.ai.entity.AiDataset">
    <!--@mbg.generated-->
    <!--@Table ai_dataset-->
    <id column="dataset_id" jdbcType="BIGINT" property="datasetId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="indexing_technique" jdbcType="VARCHAR" property="indexingTechnique" />
    <result column="process_rule" jdbcType="VARCHAR" property="processRule" />
    <result column="doc_form" jdbcType="VARCHAR" property="docForm" />
    <result column="retrieval_model" jdbcType="INTEGER" property="retrievalModel" />
    <result column="backend_id" jdbcType="VARCHAR" property="backendId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    dataset_id, title, icon, indexing_technique, process_rule, doc_form, retrieval_model, 
    backend_id
  </sql>
</mapper>