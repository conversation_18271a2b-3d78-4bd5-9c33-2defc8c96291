<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.SysPublicParamMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.coocare.ai.entity.sys.SysPublicParam">
        <id column="public_id" property="publicId"/>
        <result column="public_name" property="publicName"/>
        <result column="public_key" property="publicKey"/>
        <result column="public_value" property="publicValue"/>
        <result column="status" property="status"/>
        <result column="validate_code" property="validateCode"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="public_type" property="publicType"/>
        <result column="system_flag" property="systemFlag"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

</mapper>
